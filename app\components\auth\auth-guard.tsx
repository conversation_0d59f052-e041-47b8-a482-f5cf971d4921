"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "@/lib/auth/client";
import { Role } from "@/lib/types/auth";

// Extend the session type to include role
interface ExtendedUser {
  id: string;
  name: string;
  email: string;
  role?: Role;
  image?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireRole?: "USER" | "ADMIN";
  redirectTo?: string;
}

export function AuthGuard({
  children,
  requireAuth = true,
  requireRole,
  redirectTo = "/login"
}: AuthGuardProps) {
  const { data: session, isPending } = useSession();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    if (isPending) return; // Still loading session

    // If authentication is required but user is not logged in
    if (requireAuth && !session?.user) {
      router.push(redirectTo);
      return;
    }

    // Cast to our extended user type with role
    const user = session?.user as ExtendedUser | undefined;

    // If specific role is required but user doesn't have it
    if (requireRole && user?.role !== requireRole) {
      const defaultRedirect = user?.role === "ADMIN" ? "/admin/dashboard" : "/user/dashboard";
      router.push(defaultRedirect);
      return;
    }

    // If user is logged in but trying to access auth pages
    if (!requireAuth && user) {
      const defaultRedirect = user.role === "ADMIN" ? "/admin/dashboard" : "/user/dashboard";
      router.push(defaultRedirect);
      return;
    }

    setIsChecking(false);
  }, [session, isPending, requireAuth, requireRole, redirectTo, router]);

  // Show loading while checking authentication
  if (isPending || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40">
        <div className="flex flex-col items-center gap-4">
          <div className="w-12 h-12 border-4 border-violet-200 border-t-violet-600 rounded-full animate-spin"></div>
          <p className="text-violet-600 dark:text-violet-400 font-medium">Memuat...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
