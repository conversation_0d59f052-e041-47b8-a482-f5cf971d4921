import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import { PaymentStatus } from "@prisma/client";
import { WhatsAppService } from "@/lib/services/whatsapp";

export const runtime = 'nodejs';

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export async function PATCH(
  request: Request,
  context: Props
) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Await params before destructuring
    const params = await context.params;
    const id = params.id;

    if (!id) {
      return NextResponse.json({ error: "Payment ID is required" }, { status: 400 });
    }

    const body = await request.json();
    const { status } = body;

    if (!status || typeof status !== 'string') {
      return NextResponse.json({ error: "Invalid status" }, { status: 400 });
    }

    const existingPayment = await prisma.payment.findUnique({
      where: { id },
      include: {
        rental: {
          include: {
            user: {
              select: {
                name: true,
                phone: true,
                email: true
              }
            },
            product: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!existingPayment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Update payment status
    const result = await prisma.payment.update({
      where: { id },
      data: {
        status: status as PaymentStatus,
        updatedAt: new Date()
      },
      include: { rental: true }
    });

    // Update rental status if payment is deposit_paid
    if (status === "DEPOSIT_PAID" && result.rental) {
      await prisma.rental.update({
        where: { id: result.rentalId },
        data: {
          status: "ACTIVE"
        }
      });

      // Kirim notifikasi WhatsApp ke admin untuk deposit payment
      console.log(`[PAYMENT_STATUS_UPDATE] Sending WhatsApp notification for deposit payment: ${result.rentalId}`);

      try {
        await WhatsAppService.sendAdminDepositNotification(
          result.rentalId,
          existingPayment.rental?.user?.name || 'Unknown Customer',
          existingPayment.rental?.user?.phone || 'No phone',
          existingPayment.rental?.user?.email || 'No email',
          existingPayment.rental?.product?.name || 'Unknown Product',
          result.deposit || 0,
          new Date()
        );

        console.log(`✅ WhatsApp admin notification sent for deposit payment: ${result.rentalId}`);
      } catch (whatsappError) {
        console.error(`❌ Failed to send WhatsApp notification for deposit payment ${result.rentalId}:`, whatsappError);
      }
    }

    // Update rental status if payment is fully_paid
    if (status === "FULLY_PAID" && result.rental) {
      await prisma.rental.update({
        where: { id: result.rentalId },
        data: {
          status: "COMPLETED"
        }
      });
    }

    // Revalidate paths
    revalidatePath("/admin/payments", "layout");
    revalidatePath("/user/payments", "layout");
    revalidatePath(`/user/payments/${id}`, "layout");
    revalidatePath("/admin/rentals", "layout");

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating payment status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}