import { getSession } from "@/lib/auth/server";
import { redirect } from "next/navigation";
import { Card, CardContent } from "@/app/components/ui/card";
import { formatDate, formatCurrency } from "@/lib/utils/format";
import { OperationStatusBadge } from "@/app/components/operation/operation-status-badge";
import { getOperationStatusCounts } from "@/lib/utils/operation-status";
import { AutoRefresh, ManualRefreshButton } from "@/app/components/operation/auto-refresh";
import Link from "next/link";
import { IoPower, IoTime, IoCalendar, IoCash, IoStatsChart, IoAlarm, IoInformationCircle, IoList, IoHourglass } from "react-icons/io5";
import { LuImage } from "react-icons/lu";
import { calculateOvertimeRate } from "@/lib/utils/calculate";
import { getUserOperations } from "@/lib/data/operation";
import { formatDuration } from "@/lib/utils/duration";

// Tambahkan dynamic export untuk memastikan halaman selalu segar
export const dynamic = 'force-dynamic';

export default async function UserOperationsPage() {
  const session = await getSession();
  if (!session?.user) {
    redirect('/login');
  }

  // Gunakan fungsi baru untuk mendapatkan operasi pengguna (already sorted by priority)
  const operations = await getUserOperations(session.user.id);

  // Get status counts using the unified status logic
  const statusCounts = getOperationStatusCounts(operations);

  return (
    <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8 max-w-6xl">
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl  mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-contain bg-right bg-no-repeat" style={{ backgroundImage: "url('/images/generator.svg')" }}></div>
        <div className="relative">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-white">Status Operasi Rental</h1>
              <p className="text-white max-w-xl mt-2">Monitor status operasi genset Anda secara real-time</p>
            </div>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <AutoRefresh intervalMs={30000} enabled={false} showIndicator={true} />
              <ManualRefreshButton />
            </div>
          </div>
          <p className="text-white mt-4 text-sm">{new Date().toLocaleDateString('id-ID', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
        </div>
      </div>

      {/* Informasi refresh halaman */}
      <div className="mb-8 p-5 bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/30 dark:to-indigo-950/30 rounded-xl shadow-sm border border-violet-100 dark:border-violet-900/50">
        <div className="flex items-start gap-3">
          <div className="bg-violet-100 dark:bg-gray-900 p-2 rounded-full">
            <IoInformationCircle className="h-6 w-6 text-violet-600 dark:text-violet-400" />
          </div>
          <div>
            <p className="font-medium text-white">Informasi Status Operasi</p>
            <p className="text-sm text-white mt-1">
              Status operasi diperbarui secara real-time oleh tim teknisi kami. Jika terjadi perubahan status (seperti operasi dimulai atau selesai) yang belum terlihat, silahkan refresh halaman ini untuk melihat status terbaru.
            </p>
          </div>
        </div>
      </div>

      {/* Status Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
        <Card className="overflow-hidden bg-white dark:bg-gray-900 border-0 shadow-lg rounded-xl hover:shadow-xl transition-all duration-300">
          <div className="h-2 bg-gradient-to-r from-yellow-400 to-yellow-500 dark:from-yellow-500 dark:to-yellow-600"></div>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-600 dark:text-yellow-400 text-sm font-medium">Menunggu Operasi</p>
                <p className="text-4xl font-bold text-yellow-700 dark:text-yellow-300 mt-1">{statusCounts.pending}</p>
              </div>
              <div className="h-14 w-14 bg-gradient-to-br from-yellow-100 to-yellow-200 dark:from-yellow-900/60 dark:to-yellow-800/60 rounded-full flex items-center justify-center shadow-inner">
                <IoPower className="h-7 w-7 text-yellow-500 dark:text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden bg-white dark:bg-gray-900 border-0 shadow-lg rounded-xl hover:shadow-xl transition-all duration-300">
          <div className="h-2 bg-gradient-to-r from-green-400 to-green-500 dark:from-green-500 dark:to-green-600"></div>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 dark:text-green-400 text-sm font-medium">Sedang Beroperasi</p>
                <p className="text-4xl font-bold text-green-700 dark:text-green-300 mt-1">{statusCounts.running}</p>
              </div>
              <div className="h-14 w-14 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/60 dark:to-green-800/60 rounded-full flex items-center justify-center shadow-inner">
                <IoStatsChart className="h-7 w-7 text-green-500 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden bg-white dark:bg-gray-900 border-0 shadow-lg rounded-xl hover:shadow-xl transition-all duration-300">
          <div className="h-2 bg-gradient-to-r from-indigo-400 to-indigo-500 dark:from-indigo-500 dark:to-indigo-600"></div>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-indigo-600 dark:text-indigo-400 text-sm font-medium">Operasi Selesai</p>
                <p className="text-4xl font-bold text-indigo-700 dark:text-indigo-300 mt-1">{statusCounts.completed}</p>
              </div>
              <div className="h-14 w-14 bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900/60 dark:to-indigo-800/60 rounded-full flex items-center justify-center shadow-inner">
                <IoTime className="h-7 w-7 text-indigo-500 dark:text-indigo-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex items-center gap-3 mb-6">
        <div className="bg-white dark:bg-gray-800 p-2 rounded-full">
          <IoList className="h-5 w-5 text-gray-600 dark:text-white" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">Daftar Operasi</h2>
      </div>

      <div className="grid gap-6">
        {operations.map((rental) => {

          // Hitung tarif overtime berdasarkan kapasitas atau tarif khusus
          const overtimeRate = calculateOvertimeRate(rental.product.capacity, rental.product.overtimeRate);
          const overtimeHours = rental.overtimeHours || 0;

          return (
            <Link key={rental.id} href={`/user/operations/${rental.id}`}>
              <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white dark:bg-gray-900 border-0 shadow-md rounded-xl group">
                <CardContent className="p-0">
                  <div className="flex flex-col md:flex-row">
                    {/* Gambar Produk */}
                    <div className="w-full md:w-72 h-60 md:h-auto flex-shrink-0 rounded-tl-xl rounded-tr-xl md:rounded-tr-none md:rounded-bl-xl overflow-hidden">
                      {rental.product.imageUrl ? (
                        <div
                          className="w-full h-full bg-center bg-cover"
                          style={{
                            backgroundImage: `linear-gradient(to top right, rgba(0,0,0,0.4), rgba(0,0,0,0.1)), url('${rental.product.imageUrl}')`,
                            minHeight: '250px'
                          }}
                        >
                          <div className="w-full h-full flex items-end p-4">
                            <div className="bg-black/60 text-white text-xs px-2 py-1 rounded">
                              {rental.product.capacity} KVA
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-700" style={{ minHeight: '250px' }}>
                          <LuImage className="w-16 h-16 text-gray-400 dark:text-gray-500" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1 p-6 space-y-4 group-hover:bg-gray-50 dark:group-hover:bg-gray-800/80 transition-colors duration-300">
                      {/* Header dengan Status */}
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 group-hover:text-blue-700 dark:group-hover:text-blue-400 transition-colors duration-300">{rental.product.name}</h3>
                          <p className="text-gray-500 dark:text-gray-400 flex items-center gap-1 mt-1">
                            <span className="inline-block w-2 h-2 rounded-full bg-gray-400 dark:bg-gray-500"></span>
                            {rental.product.capacity} KVA
                          </p>
                        </div>
                        <div className="mt-1">
                          <OperationStatusBadge rental={rental} />
                        </div>
                      </div>

                      {/* Informasi Operasional */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg dark:group-hover:bg-gray-800 transition-colors duration-300">
                        <div className="flex items-center gap-3 text-gray-700 dark:text-gray-300">
                          <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900/30 transition-colors duration-300">
                            <IoCalendar className="w-5 h-5 text-blue-500 dark:text-blue-400" />
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Periode Sewa</p>
                            <p className="font-medium">{formatDate(rental.startDate)} - {formatDate(rental.endDate)}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 text-gray-700 dark:text-gray-300">
                          <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-full group-hover:bg-violet-50 dark:group-hover:bg-violet-900/30 transition-colors duration-300">
                            <IoHourglass className="w-5 h-5 text-violet-500 dark:text-violet-400" />
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Durasi</p>
                            <p className="font-medium">
                              {formatDuration(rental.duration)}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 text-gray-700 dark:text-gray-300">
                          <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-full group-hover:bg-red-50 dark:group-hover:bg-red-900/30 transition-colors duration-300">
                            <IoTime className="w-5 h-5 text-red-500 dark:text-red-400" />
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Waktu Kedatangan</p>
                            <p className="font-medium">{rental.arrivalTime}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 text-gray-700 dark:text-gray-300">
                          <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-full group-hover:bg-green-50 dark:group-hover:bg-green-900/30 transition-colors duration-300">
                            <IoCash className="w-5 h-5 text-green-500 dark:text-green-400" />
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Biaya</p>
                            <p className="font-medium text-green-600 dark:text-green-400">{formatCurrency(rental.amount)}</p>
                          </div>
                        </div>
                      </div>

                      {/* Status Operasi */}
                      <div className="border-t border-gray-100 dark:border-gray-700 pt-4 mt-4">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-full ${!rental.operationalStart ? 'bg-yellow-100 dark:bg-yellow-900/40 group-hover:bg-yellow-200 dark:group-hover:bg-yellow-800/60' : !rental.operationalEnd ? 'bg-sky-100 dark:bg-sky-900/40 group-hover:bg-sky-200 dark:group-hover:bg-sky-800/60' : 'bg-blue-100 dark:bg-blue-900/40 group-hover:bg-blue-200 dark:group-hover:bg-blue-800/60'} transition-colors duration-300`}>
                            <IoPower className={`w-5 h-5 ${!rental.operationalStart ? 'text-yellow-500 dark:text-yellow-400' : !rental.operationalEnd ? 'text-sky-500 dark:text-sky-400' : 'text-blue-500 dark:text-blue-400'}`} />
                          </div>
                          <div>
                            {!rental.operationalStart && (
                              <p className="text-yellow-600 dark:text-yellow-400 font-medium">Menunggu tim teknisi memulai operasi</p>
                            )}
                            {rental.operationalStart && !rental.operationalEnd && (
                              <div>
                                <p className="text-sky-600 dark:text-sky-400 font-medium">Sedang Beroperasi</p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  Dimulai: {formatDate(rental.operationalStart)}
                                </p>
                                {/* Show that overtime has a different rate based on capacity */}
                                <div className="mt-2 bg-orange-50 dark:bg-orange-900/20 py-1 px-3 rounded-md flex items-center gap-1">
                                  <IoAlarm className="w-4 h-4 text-orange-500 dark:text-orange-400" />
                                  <p className="text-sm text-orange-700 dark:text-orange-300">
                                    Tarif overtime: {formatCurrency(overtimeRate)}/jam
                                  </p>
                                </div>
                              </div>
                            )}
                            {rental.operationalEnd && (
                              <div>
                                <p className="text-blue-600 dark:text-blue-400 font-medium">Operasi Selesai</p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  Selesai: {formatDate(rental.operationalEnd)}
                                </p>
                                {overtimeHours > 0 && (
                                  <div className="mt-2 bg-orange-50 dark:bg-orange-900/20 py-1 px-3 rounded-md flex items-center gap-1">
                                    <IoAlarm className="w-4 h-4 text-orange-500 dark:text-orange-400" />
                                    <p className="text-sm text-orange-700 dark:text-orange-300">
                                      Overtime: {overtimeHours} jam ({formatCurrency(overtimeHours * overtimeRate)})
                                    </p>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          );
        })}

        {operations.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="flex flex-col items-center gap-4">
                <IoPower className="w-12 h-12 text-gray-300" />
                <div>
                  <p className="text-gray-500 text-lg">Belum ada operasi rental yang aktif</p>
                  <Link
                    href="/user/catalog"
                    className="text-blue-600 hover:text-blue-700 mt-2 inline-block"
                  >
                    Lihat Katalog Genset
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
