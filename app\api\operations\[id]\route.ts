import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { revalidatePath } from "next/cache";
import { calculateOvertimeRate, getBookedHours, calculateOperationalOvertime } from "@/lib/utils/calculate";
import { notifyOperationStarted, notifyOperationCompleted, notifyOvertime } from "@/lib/notifications";

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    const { id: rentalId } = await params;

    // Validasi rental ID
    if (!rentalId) {
      return NextResponse.json(
        { error: "Rental ID is required" },
        { status: 400 }
      );
    }

    // Periksa apakah rental ada
    const existingRental = await prisma.rental.findUnique({
      where: { id: rentalId },
      include: {
        payment: true,
        product: true,
        user: {
          select: {
            id: true
          }
        }
      }
    });

    if (!existingRental) {
      return NextResponse.json(
        { error: "Rental not found" },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { action, startTime, endTime, hours, overtimeHours } = body;

    // Validasi action
    if (!action || (action !== "start" && action !== "end" && action !== "mark_paid")) {
      return NextResponse.json(
        { error: "Invalid action. Must be 'start', 'end', or 'mark_paid'" },
        { status: 400 }
      );
    }

    // Persiapkan data update
    const updateData: {
      operationalStart?: Date;
      operationalEnd?: Date;
      status?: "PENDING" | "CONFIRMED" | "ACTIVE" | "COMPLETED" | "CANCELLED";
      hours?: number;
      overtime?: number;
    } = {};

    // Variabel untuk mencatat jika ada overtime
    let hasOvertime = false;
    let overtimeAmount = 0;

    if (action === "start") {
      if (!startTime) {
        return NextResponse.json(
          { error: "Start time is required for 'start' action" },
          { status: 400 }
        );
      }
      updateData.operationalStart = new Date(startTime);
      updateData.status = "ACTIVE"; // Update status rental menjadi ACTIVE
    }

    if (action === "end") {
      if (!endTime) {
        return NextResponse.json(
          { error: "End time is required for 'end' action" },
          { status: 400 }
        );
      }

      // Update data operasi
      updateData.operationalEnd = new Date(endTime);
      updateData.status = "COMPLETED"; // Update status rental menjadi COMPLETED

      // Hitung overtime otomatis jika tidak ada jam overtime yang diberikan
      if (overtimeHours === undefined && existingRental.endDate) {
        const operationalEndTime = new Date(endTime);

        // Hitung durasi yang dipesan dalam jam
        let bookedHours = 0;
        const duration = existingRental.duration || "8_HOURS";

        // Parse durasi berdasarkan format yang digunakan
        if (typeof duration === 'number') {
          bookedHours = duration;
        } else if (typeof duration === 'string') {
          bookedHours = getBookedHours(duration);
        } else {
          bookedHours = 8; // Default jika tidak ada durasi yang valid
        }

        // Waktu mulai operasional
        const operationalStartTime = existingRental.operationalStart ?
          new Date(existingRental.operationalStart) :
          new Date(existingRental.startDate);

        // Ambil data pause dari metadata timer dalam notes
        let pauseOffset = 0;

        if (existingRental.notes && existingRental.notes.includes("TIMER_STATUS:")) {
          const timerMetadataMatch = existingRental.notes.match(/TIMER_STATUS: (.*?)(\n|$)/);
          if (timerMetadataMatch && timerMetadataMatch[1]) {
            const [isPaused, pauseOffsetStr, pauseStartTimeStr] = timerMetadataMatch[1].split(",");
            pauseOffset = parseInt(pauseOffsetStr || "0", 10);

            // Jika timer sedang dijeda saat dihentikan, tambahkan waktu jeda terakhir
            if (isPaused === "true") {
              const pauseStartTime = parseInt(pauseStartTimeStr || "0", 10);
              if (pauseStartTime > 0) {
                const additionalPauseTime = operationalEndTime.getTime() - pauseStartTime;
                if (additionalPauseTime > 0) {
                  pauseOffset += additionalPauseTime;
                }
              }
            }
          }
        }

        console.log(`Rental ${rentalId} - Booked hours: ${bookedHours}, Paused for: ${pauseOffset/1000/60} minutes`);

        // Hitung overtime menggunakan fungsi yang lebih akurat
        const calculatedOvertimeHours = calculateOperationalOvertime(
          operationalStartTime,
          operationalEndTime,
          bookedHours,
          pauseOffset
        );

        // Simpan hasil overtime ke rental
        if (calculatedOvertimeHours > 0) {
          updateData.overtimeHours = calculatedOvertimeHours;
          hasOvertime = true;

          console.log(`Rental ${rentalId} overtime: ${calculatedOvertimeHours} hours`);

          // Hitung biaya overtime
          if (existingRental.product) {
            const capacity = existingRental.product.capacity;
            const overtimeRate = calculateOvertimeRate(capacity, existingRental.product.overtimeRate);
            overtimeAmount = calculatedOvertimeHours * overtimeRate;

            // Update jumlah tagihan dengan overtime
            if (existingRental.payment) {
              // Hitung sisa pembayaran yang benar (50% dari jumlah awal + overtime)
              const remainingAmount = Math.floor(existingRental.amount * 0.5) + overtimeAmount;

              // Tambahkan biaya overtime ke remaining yang ada
              await prisma.payment.update({
                where: { id: existingRental.payment.id },
                data: {
                  overtimeCost: overtimeAmount,
                  remaining: remainingAmount,
                  updatedAt: new Date()
                }
              });
            }
          }
        }
      }

      // Tambahan parameter opsional
      if (hours !== undefined) {
        updateData.hours = hours;
      }

      // Gunakan parameter overtimeHours jika diberikan secara eksplisit
      if (overtimeHours !== undefined) {
        updateData.overtime = overtimeHours;
        hasOvertime = overtimeHours > 0;

        // Hitung biaya overtime jika ada
        if (overtimeHours > 0 && existingRental.product) {
          const capacity = existingRental.product.capacity;
          const overtimeRate = calculateOvertimeRate(capacity, existingRental.product.overtimeRate);
          overtimeAmount = overtimeHours * overtimeRate;

          // Update jumlah tagihan dengan overtime
          // Pastikan payment ada
          if (existingRental.payment) {
            // Hitung sisa pembayaran yang benar (50% dari jumlah awal + overtime)
            const remainingAmount = Math.floor(existingRental.amount * 0.5) + overtimeAmount;

            // Tambahkan biaya overtime ke remaining yang ada
            await prisma.payment.update({
              where: { id: existingRental.payment.id },
              data: {
                overtimeCost: overtimeAmount,
                remaining: remainingAmount,
                updatedAt: new Date()
              }
            });
          }
        }
      }
    }

    if (action === "mark_paid") {
      updateData.status = "COMPLETED"; // Update status rental menjadi COMPLETED

      // Jika ada payment, update statusnya menjadi fully_paid
      if (existingRental.payment) {
        await prisma.payment.update({
          where: { id: existingRental.payment.id },
          data: {
            status: "FULLY_PAID",
            updatedAt: new Date()
          }
        });
      } else {
        // Jika tidak ada payment, buat payment baru
        await prisma.payment.create({
          data: {
            rentalId: rentalId,
            userId: existingRental.userId,
            amount: existingRental.amount,
            deposit: existingRental.amount * 0.5, // 50% dari total amount
            remaining: existingRental.amount * 0.5,
            status: "FULLY_PAID",
            transactionId: `MANUAL-${Date.now()}`
          }
        });
      }
    }

    // Lakukan update rental
    const rental = await prisma.rental.update({
      where: { id: rentalId },
      data: updateData
    });

    // Kirim notifikasi berdasarkan jenis action
    if (action === "start") {
      // Notifikasi operasi dimulai
      if (existingRental.user?.id && existingRental.product?.name) {
        await notifyOperationStarted(
          existingRental.user.id,
          rentalId,
          existingRental.product.name
        );
      }
    } else if (action === "end") {
      // Notifikasi operasi selesai
      if (existingRental.user?.id && existingRental.product?.name) {
        await notifyOperationCompleted(
          existingRental.user.id,
          rentalId,
          existingRental.product.name
        );

        // Notifikasi overtime jika ada
        if (hasOvertime && overtimeAmount > 0) {
          await notifyOvertime(
            existingRental.user.id,
            rentalId,
            existingRental.product.name,
            overtimeAmount
          );
        }
      }
    }

    // Revalidasi path terkait dengan force revalidation
    revalidatePath("/admin/operations", "layout");
    revalidatePath(`/admin/operations/${rentalId}`, "layout");
    revalidatePath("/admin/payments", "layout");
    revalidatePath(`/user/rentals/${rentalId}`, "layout");
    revalidatePath(`/user/operations/${rentalId}`, "layout");
    revalidatePath("/user/operations", "layout");
    revalidatePath("/user/payments", "layout");
    revalidatePath("/admin/dashboard", "layout");
    revalidatePath("/user/dashboard", "layout");

    console.log(`Rental ${rentalId} operation updated with action: ${action}`);

    return NextResponse.json({ success: true, data: rental });
  } catch (error) {
    console.error("Operation error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update operation status" },
      { status: 500 }
    );
  }
}