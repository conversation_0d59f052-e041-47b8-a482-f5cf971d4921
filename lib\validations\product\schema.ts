import { z } from "zod";

/**
 * Schema validasi untuk produk
 */
export const ProductSchema = z.object({
  name: z.string({
    required_error: "Nama produk harus diisi",
    invalid_type_error: "Nama produk harus berupa teks",
  }).min(1, "Nama produk harus diisi"),

  price: z.number({
    required_error: "Harga harus diisi",
    invalid_type_error: "Harga harus berupa angka",
  }).min(1, "Harga harus diisi"),

  capacity: z.number({
    required_error: "Kapasitas harus diisi",
    invalid_type_error: "Kapasitas harus berupa angka",
  }).min(1, "Kapasitas harus diisi"),

  stock: z.number({
    required_error: "Stok harus diisi",
    invalid_type_error: "Stok harus berupa angka",
  }).min(0, "Stok tidak boleh negatif"),

  overtimeRate: z.number({
    required_error: "Tarif overtime harus diisi",
    invalid_type_error: "Tarif overtime harus berupa angka",
  }).min(0, "Tarif overtime tidak boleh negatif").max(10000000, "Tarif overtime maksimal 10 juta rupiah"),

  description: z.string().nullable(),
  category: z.string().nullable(),
  imageUrl: z.string().nullable(),

  status: z.enum(["AVAILABLE", "NOT_AVAILABLE", "MAINTENANCE"], {
    required_error: "Status harus diisi",
    invalid_type_error: "Status tidak valid",
  }).default("AVAILABLE"),
});

// Tipe-tipe yang diekspor
export type ProductInput = z.infer<typeof ProductSchema>;

// Membuat objek schemas untuk ekspor
const productSchemas = {
  ProductSchema
};

export default productSchemas;
