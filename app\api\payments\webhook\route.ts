import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import MidtransService from "@/lib/services/midtrans";
import type { MidtransNotification } from "@/lib/types/payment";
import { notifyPaymentSuccess, notifyPaymentFailed } from "@/lib/notifications";
import { WhatsAppService } from "@/lib/services/whatsapp";

// Payment status constants menggunakan enum values
const PaymentStatus = {
  DEPOSIT_PENDING: 'DEPOSIT_PENDING',
  DEPOSIT_PAID: 'DEPOSIT_PAID',
  FULLY_PAID: 'FULLY_PAID',
  FAILED: 'FAILED',
  INVOICE_ISSUED: 'INVOICE_ISSUED'
} as const;

type PaymentStatusType = typeof PaymentStatus[keyof typeof PaymentStatus];

export const runtime = 'nodejs';

export async function POST(request: Request) {
  try {
    const body = await request.json() as MidtransNotification;

    console.log("[PAYMENT_WEBHOOK] Received webhook:", {
      order_id: body.order_id,
      transaction_status: body.transaction_status,
      fraud_status: body.fraud_status
    });

    // Verifikasi signature dari Midtrans
    if (!MidtransService.verifySignature(body)) {
      console.log("[PAYMENT_WEBHOOK] Invalid signature");
      return new NextResponse("Invalid signature", { status: 403 });
    }

    const [orderId, paymentType] = body.order_id.split('_');
    const transactionStatus = body.transaction_status;
    const fraudStatus = body.fraud_status;

    console.log("[PAYMENT_WEBHOOK] Parsed data:", {
      orderId,
      paymentType,
      transactionStatus,
      fraudStatus
    });

    const payment = await prisma.payment.findUnique({
      where: { rentalId: orderId },
      include: {
        rental: {
          include: {
            product: {
              select: {
                name: true
              }
            },
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true
              }
            }
          }
        }
      },
    });

    if (!payment) {
      console.log("[PAYMENT_WEBHOOK] Payment not found for rental ID:", orderId);
      return new NextResponse("Payment not found", { status: 404 });
    }

    console.log("[PAYMENT_WEBHOOK] Found payment:", {
      paymentId: payment.id,
      currentStatus: payment.status,
      rentalId: payment.rentalId
    });

    // Update status berdasarkan response Midtrans dan tipe pembayaran
    const status = getPaymentStatus(transactionStatus, fraudStatus, paymentType);

    console.log("[PAYMENT_WEBHOOK] New status will be:", status);

    await prisma.$transaction(async (tx) => {
      // Update payment
      await tx.payment.update({
        where: { rentalId: orderId },
        data: {
          status,
          transactionId: body.transaction_id
        }
      });

      // Update rental status jika pembayaran deposit berhasil
      if (status === PaymentStatus.DEPOSIT_PAID) {
        await tx.rental.update({
          where: { id: payment.rentalId },
          data: {
            status: "ACTIVE"
          }
        });
      }
    });

    // Kirim notifikasi berdasarkan status pembayaran
    if (status === PaymentStatus.DEPOSIT_PAID || status === PaymentStatus.FULLY_PAID) {
      // Jika pembayaran berhasil
      const userId = payment.rental.user.id;
      const amount = parseFloat(body.gross_amount);
      const productName = payment.rental.product.name;
      const paymentTypeLabel = status === PaymentStatus.DEPOSIT_PAID ? "deposit" : "pembayaran penuh";

      await notifyPaymentSuccess(
        userId,
        orderId,
        amount,
        `${paymentTypeLabel} ${productName}`
      );

      // Kirim notifikasi WhatsApp ke admin untuk deposit payment
      if (status === PaymentStatus.DEPOSIT_PAID) {
        console.log(`[PAYMENT_WEBHOOK] Sending WhatsApp notification for deposit payment: ${orderId}`);

        try {
          await WhatsAppService.sendAdminDepositNotification(
            orderId,
            payment.rental.user.name || 'Unknown Customer',
            payment.rental.user.phone || 'No phone',
            payment.rental.user.email || 'No email',
            productName,
            amount,
            new Date()
          );

          console.log(`✅ WhatsApp admin notification sent for deposit payment: ${orderId}`);
        } catch (whatsappError) {
          console.error(`❌ Failed to send WhatsApp notification for deposit payment ${orderId}:`, whatsappError);
        }
      }

    } else if (status === PaymentStatus.FAILED) {
      // Jika pembayaran gagal
      const userId = payment.rental.user.id;
      const amount = parseFloat(body.gross_amount);
      const productName = payment.rental.product.name;
      const paymentTypeLabel = paymentType === "deposit" ? "deposit" : "pembayaran penuh";

      await notifyPaymentFailed(
        userId,
        orderId,
        amount,
        `${paymentTypeLabel} ${productName}`
      );
    }

    // Revalidate paths
    revalidatePath("/", "layout");
    revalidatePath("/admin/payments", "layout");
    revalidatePath("/user/payments", "layout");
    revalidatePath(`/user/payments/${orderId}`, "layout");

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[PAYMENT_WEBHOOK]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

function getPaymentStatus(
  transactionStatus: string,
  fraudStatus: string | undefined,
  paymentType: string
): PaymentStatusType {
  if (transactionStatus === "capture") {
    if (fraudStatus === "accept") {
      return paymentType === "deposit" ? PaymentStatus.DEPOSIT_PAID : PaymentStatus.FULLY_PAID;
    }
    return paymentType === "deposit" ? PaymentStatus.DEPOSIT_PENDING : PaymentStatus.DEPOSIT_PENDING;
  }

  if (transactionStatus === "settlement") {
    return paymentType === "deposit" ? PaymentStatus.DEPOSIT_PAID : PaymentStatus.FULLY_PAID;
  }

  if (["cancel", "deny", "expire"].includes(transactionStatus)) {
    return PaymentStatus.FAILED;
  }

  return paymentType === "deposit" ? PaymentStatus.DEPOSIT_PENDING : PaymentStatus.DEPOSIT_PENDING;
}

