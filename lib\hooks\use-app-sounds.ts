import useSound from 'use-sound';

// Hook untuk mengelola semua suara aplikasi
export function useAppSounds() {
  // Load semua file suara
  const [playAlert] = useSound('/sound/alert.mp3', { volume: 0.6 });
  const [playError] = useSound('/sound/error.mp3', { volume: 0.7 });
  const [playNotification] = useSound('/sound/notification.mp3', { volume: 0.6 });
  const [playSuccess] = useSound('/sound/success.mp3', { volume: 0.6 });

  return {
    // Suara untuk notifikasi umum
    playAlert,

    // Suara untuk error/gagal
    playError,

    // Suara untuk notifikasi biasa
    playNotification,

    // Suara untuk sukses/berhasil
    playSuccess,

    // Helper functions untuk konteks spesifik
    sounds: {
      // Operasi rental
      onOperationStart: () => playNotification(),
      onOperationComplete: () => playSuccess(),
      onOperationAlert: () => playAlert(),

      // Booking/Order
      onBookingSuccess: () => playSuccess(),
      onBookingError: () => playError(),

      // UI Interactions - no sound for button clicks
      onButtonClick: () => {}, // No sound
      onFormSubmit: () => {}, // No sound

      // Status changes
      onStatusUpdate: () => playNotification(),
      onPaymentSuccess: () => playSuccess(),
      onPaymentError: () => playError(),

      // General notifications
      onSuccess: () => playSuccess(),
      onError: () => playError(),
      onAlert: () => playAlert(),
      onNotification: () => playNotification(),
    }
  };
}

// Hook untuk suara dengan kontrol volume dan enable/disable
export function useAppSoundsWithSettings() {
  const sounds = useAppSounds();
  
  // Bisa ditambahkan logic untuk settings suara dari localStorage
  const isSoundEnabled = true; // Nanti bisa dari settings user
  
  const playWithCheck = (soundFunction: () => void) => {
    if (isSoundEnabled) {
      soundFunction();
    }
  };

  return {
    ...sounds,
    isSoundEnabled,
    playWithCheck,
    // Override sounds dengan check
    sounds: {
      ...sounds.sounds,
      onOperationStart: () => playWithCheck(sounds.sounds.onOperationStart),
      onOperationComplete: () => playWithCheck(sounds.sounds.onOperationComplete),
      onOperationAlert: () => playWithCheck(sounds.sounds.onOperationAlert),
      onBookingSuccess: () => playWithCheck(sounds.sounds.onBookingSuccess),
      onBookingError: () => playWithCheck(sounds.sounds.onBookingError),
      onButtonClick: () => {}, // No sound for button clicks
      onFormSubmit: () => {}, // No sound for form submit
      onStatusUpdate: () => playWithCheck(sounds.sounds.onStatusUpdate),
      onPaymentSuccess: () => playWithCheck(sounds.sounds.onPaymentSuccess),
      onPaymentError: () => playWithCheck(sounds.sounds.onPaymentError),
      onSuccess: () => playWithCheck(sounds.sounds.onSuccess),
      onError: () => playWithCheck(sounds.sounds.onError),
      onAlert: () => playWithCheck(sounds.sounds.onAlert),
      onNotification: () => playWithCheck(sounds.sounds.onNotification),
    }
  };
}
