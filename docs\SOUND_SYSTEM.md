# Sistem Suara Aplikasi Rental Genset

## Overview

Sistem suara telah diimplementasikan menggunakan library `use-sound` untuk memberikan feedback audio yang meningkatkan user experience aplikasi rental genset.

## File Audio yang Tersedia

Lokasi: `public/sound/`

1. **alert.mp3** - Suara peringatan untuk situasi yang memerlukan perhatian
2. **error.mp3** - Suara error untuk kesalahan atau kegagalan
3. **notification.mp3** - Suara notifikasi untuk informasi umum
4. **success.mp3** - Suara sukses untuk konfirmasi berhasil

## Komponen dan Hook yang Tersedia

### 1. `useAppSounds()` Hook

Hook utama untuk mengelola semua suara aplikasi.

```typescript
import { useAppSounds } from "@/lib/hooks/use-app-sounds";

const { sounds } = useAppSounds();

// Contoh penggunaan
sounds.onOperationStart(); // Suara saat operasi dimulai
sounds.onBookingSuccess(); // Suara saat booking berhasil
sounds.onButtonClick(); // Tidak ada suara (disabled)
```

### 2. `SoundButton` Component

Komponen tombol dengan efek suara built-in.

```tsx
import { SoundButton } from "@/app/components/ui/sound-button";

<SoundButton variant="gradient" soundType="success" onClick={handleSubmit}>
  Kirim Pesanan
</SoundButton>;
```

**Props:**

- `soundType`: "click" | "success" | "error" | "none"
- Semua props Button lainnya tetap tersedia

### 3. `useSoundToast()` Hook

Hook untuk toast notifications dengan suara.

```typescript
import { useSoundToast } from "@/app/components/ui/sound-toast";

const soundToast = useSoundToast();

soundToast.success("Pesanan berhasil!");
soundToast.error("Terjadi kesalahan!");
soundToast.warning("Peringatan!");
soundToast.info("Informasi status");
```

## Implementasi di Aplikasi

### 1. Halaman Catalog (`/user/catalog`)

- ✅ Tombol filter dengan suara klik
- ✅ Tombol "Detail" dan "Sewa" dengan suara
- ✅ Tombol "Hapus Filter" dengan suara

### 2. Form Rental (`/user/catalog/[id]/rent`)

- ✅ Tombol "Lanjutkan" dengan suara klik
- ✅ Tombol "Kembali" dengan suara klik
- ✅ Tombol "Kirim Pesanan" dengan suara sukses
- ✅ Toast notifications dengan suara untuk validasi

### 3. Halaman Operations (`/user/operations`)

- ✅ Auto-refresh dengan suara klik
- ✅ Notifikasi suara saat status operasi berubah
- ✅ Manual refresh button dengan suara

### 4. Halaman Payments (`/user/payments`)

- ✅ Tombol "Sewa Genset Baru" dengan suara klik
- ✅ Tombol "Lihat Katalog Genset" dengan suara klik
- ✅ Tombol "Bayar Deposit Sekarang" dengan suara sukses
- ✅ Tombol "Lunasi Pembayaran" dengan suara klik
- ✅ Tombol pagination dengan suara klik
- ✅ Tombol "Refresh Status" dengan suara klik
- ✅ Tombol pembayaran dengan suara klik dan feedback sukses/error
- ✅ Suara otomatis saat pembayaran berhasil/gagal/pending

### 5. Animasi Pembayaran GSAP (`PaymentAnimation`)

- ✅ Animasi success dengan partikel dan bounce effect
- ✅ Animasi error dengan shake effect
- ✅ Animasi processing dengan rotasi kontinyu
- ✅ Integrasi dengan sistem sound otomatis
- ✅ Backdrop blur dan overlay responsif
- ✅ Auto-hide setelah animasi selesai

### 6. Halaman Test (`/user/sound-test` & `/user/animation-test`)

- ✅ Demo semua efek suara yang tersedia
- ✅ Demo animasi pembayaran GSAP
- ✅ Contoh penggunaan setiap komponen

## Konteks Penggunaan Suara

### Operasi Rental

```typescript
sounds.onOperationStart(); // Saat operasi genset dimulai
sounds.onOperationComplete(); // Saat operasi selesai
sounds.onOperationAlert(); // Alert terkait operasi
sounds.onStatusUpdate(); // Saat status berubah
```

### Booking & Pembayaran

```typescript
sounds.onBookingSuccess(); // Booking berhasil
sounds.onBookingError(); // Booking gagal
sounds.onPaymentSuccess(); // Pembayaran berhasil
sounds.onPaymentError(); // Pembayaran gagal
```

### Interaksi UI

```typescript
sounds.onButtonClick(); // Tidak ada suara (disabled)
sounds.onFormSubmit(); // Tidak ada suara (disabled)
sounds.onSuccess(); // Aksi berhasil umum
sounds.onError(); // Error umum
sounds.onAlert(); // Peringatan umum
sounds.onNotification(); // Notifikasi umum
```

## Konfigurasi Volume

Volume default untuk setiap suara:

- Alert: 0.6
- Error: 0.7
- Notification: 0.6
- Success: 0.6

## Fitur Tambahan

### 1. Sound Settings (Future Enhancement)

Hook `useAppSoundsWithSettings()` sudah disiapkan untuk implementasi pengaturan suara user di masa depan.

### 2. Conditional Sound Playing

Suara dapat dimatikan berdasarkan kondisi tertentu:

```typescript
soundType={isSubmitting ? "none" : "success"}
```

### 3. Auto Sound pada Status Change

Sistem otomatis memainkan suara saat:

- Status operasi berubah (polling)
- Refresh manual dilakukan
- Form berhasil disubmit

## Testing

Kunjungi `/user/sound-test` untuk menguji semua efek suara yang tersedia.

## Browser Compatibility

Library `use-sound` mendukung:

- Chrome/Edge: ✅ Full support
- Firefox: ✅ Full support
- Safari: ✅ Full support
- Mobile browsers: ✅ Full support

## Performance Notes

- File audio di-load on-demand saat hook pertama kali digunakan
- Volume rendah untuk menghindari gangguan
- Tidak ada autoplay yang melanggar browser policies
- Suara hanya dimainkan sebagai respons terhadap user interaction

## Future Enhancements

1. **User Settings**: Pengaturan untuk enable/disable suara
2. **Custom Sounds**: Upload suara kustom per user
3. **Sound Themes**: Tema suara yang berbeda
4. **Volume Control**: Kontrol volume individual per jenis suara
5. **Sound Visualization**: Indikator visual saat suara dimainkan
