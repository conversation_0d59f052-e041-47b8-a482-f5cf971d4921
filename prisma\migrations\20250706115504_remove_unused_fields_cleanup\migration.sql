/*
  Warnings:

  - The values [MAINTENANCE] on the enum `ProductStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `overtime` on the `Payment` table. All the data in the column will be lost.
  - You are about to drop the column `snapToken` on the `Payment` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `Payment` table. All the data in the column will be lost.
  - You are about to drop the column `location` on the `rentals` table. All the data in the column will be lost.
  - You are about to drop the column `overtime` on the `rentals` table. All the data in the column will be lost.
  - You are about to drop the column `paymentStatus` on the `rentals` table. All the data in the column will be lost.
  - You are about to drop the column `quantity` on the `rentals` table. All the data in the column will be lost.
  - You are about to drop the `Maintenance` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Review` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `rate_limits` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `verification_tokens` table. If the table is not empty, all the data it contains will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ProductStatus_new" AS ENUM ('AVAILABLE', 'NOT_AVAILABLE');
ALTER TABLE "Product" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "Product" ALTER COLUMN "status" TYPE "ProductStatus_new" USING ("status"::text::"ProductStatus_new");
ALTER TYPE "ProductStatus" RENAME TO "ProductStatus_old";
ALTER TYPE "ProductStatus_new" RENAME TO "ProductStatus";
DROP TYPE "ProductStatus_old";
ALTER TABLE "Product" ALTER COLUMN "status" SET DEFAULT 'AVAILABLE';
COMMIT;

-- DropIndex
DROP INDEX "Payment_userId_idx";

-- AlterTable
ALTER TABLE "Payment" DROP COLUMN "overtime",
DROP COLUMN "snapToken",
DROP COLUMN "userId",
ADD COLUMN     "overtimeCost" DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "rentals" DROP COLUMN "location",
DROP COLUMN "overtime",
DROP COLUMN "paymentStatus",
DROP COLUMN "quantity",
ADD COLUMN     "overtimeHours" DOUBLE PRECISION DEFAULT 0;

-- DropTable
DROP TABLE "Maintenance";

-- DropTable
DROP TABLE "Review";

-- DropTable
DROP TABLE "rate_limits";

-- DropTable
DROP TABLE "verification_tokens";

-- DropEnum
DROP TYPE "MaintenanceType";
