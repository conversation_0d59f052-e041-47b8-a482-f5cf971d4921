import Link from "next/link";
import { LuArrowLeft, LuPackage } from "react-icons/lu";
import { SoundButton } from "@/app/components/ui/sound-button";

export default function ProductNotFound() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <div className="mb-6">
            <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
              <LuPackage className="h-8 w-8 text-gray-400 dark:text-gray-500" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Produk Tidak Ditemukan
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Produk yang Anda cari tidak tersedia atau mungkin telah dihapus.
            </p>
          </div>

          <div className="space-y-3">
            <Link href="/user/catalog" className="block">
              <SoundButton
                variant="gradient"
                className="w-full"
                soundType="none"
              >
                <LuArrowLeft className="h-4 w-4 mr-2" />
                Kembali ke Katalog
              </SoundButton>
            </Link>

            <Link href="/user/debug-products" className="block">
              <SoundButton
                variant="outline"
                className="w-full"
                soundType="none"
              >
                Lihat Semua Produk
              </SoundButton>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
