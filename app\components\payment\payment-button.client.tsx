"use client";

import { useState } from "react";
import { SoundButton } from "@/app/components/ui/sound-button";
import { LuCreditCard } from "react-icons/lu";
import { useToast } from "@/lib/hooks/use-toast";
import { usePaymentFlow } from "@/lib/hooks/use-payment-flow";

interface PaymentButtonProps {
  rentalId: string;
  amount: number;
  type: "deposit" | "remaining" | "full";
  label?: string;
  colorClass?: string;
  onSuccess?: (data: PaymentResponse) => void;
}

interface PaymentResponse {
  success: boolean;
  message: string;
  token?: string;
  paymentId?: string;
  redirectUrl?: string;
}

export function PaymentButton({
  rentalId,
  amount,
  type,
  label = "Bayar Sekarang",
  onSuccess
}: PaymentButtonProps) {
  const [loading, setLoading] = useState(false);
  const { showError } = useToast();
  const { processPayment } = usePaymentFlow();

  const handlePayment = async () => {
    try {
      // Check if Snap is available on window
      if (typeof window !== 'undefined' && !window.snap) {
        showError('Sistem pembayaran belum siap. Pastikan file .env.local berisi NEXT_PUBLIC_MIDTRANS_CLIENT_KEY.');
        return;
      }

      setLoading(true);
      await processPayment({
        rentalId,
        type,
        amount,
        onSuccess,
        onError: (error) => {
          console.error("Payment error:", error);
          showError(error.message || 'Terjadi kesalahan dalam proses pembayaran');
        }
      });
    } catch (error) {
      console.error("Payment failed:", error);
      showError('Gagal memproses pembayaran. Silakan coba lagi nanti.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SoundButton
      onClick={handlePayment}
      disabled={loading}
      loading={loading}
      variant={type === "deposit" ? "success" : "gradient"}
      size="mobile"
      className="text-white shadow-lg hover:shadow-xl"
      soundType="none"
    >
      {!loading && <LuCreditCard className="mr-2 h-4 w-4" />}
      {loading ? "Memproses..." : label}
    </SoundButton>
  );
}
