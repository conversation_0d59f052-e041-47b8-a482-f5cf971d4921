# 📖 CARA MEMBACA DFD LEVEL 0 & LEVEL 1 - SISTEM RENTAL GANSET

## 🎯 TUJUAN DOKUMEN

Dokumen ini menjelaskan secara khusus cara membaca dan memahami DFD Level 0 (Context Diagram) dan Level 1 (Proses Utama) untuk Sistem Rental Ganset.

## 📊 DFD LEVEL 0 (CONTEXT DIAGRAM)

### 🔍 CARA MEMBACA LEVEL 0

#### 1️⃣ **IDENTIFIKASI KOMPONEN**

**🔵 Sistem Utama (Tengah):**

```
    ┌─────────────────────┐
    │   Sistem Rental     │
    │      Ganset         │
    │         (0)         │
    └─────────────────────┘
```

- **Simbol:** Lingkaran besar atau kotak dengan sudut membulat
- **Nomor:** 0 (menunjukkan level tertinggi)
- **Nama:** Sistem Rental Ganset
- **Fungsi:** Representasi seluruh sistem sebagai satu kesatuan

**⬜ External Entities (Pinggir):**

```
┌─────────────┐                    ┌─────────────┐
│  Customer   │                    │    Admin    │
│    /User    │                    │             │
└─────────────┘                    └─────────────┘
```

- **Simbol:** Persegi panjang
- **Posisi:** Di luar boundary sistem
- **Jumlah:** 2 entities (Customer/User, Admin)
- **Fungsi:** Sumber dan tujuan data dari/ke sistem

#### 2️⃣ **MEMBACA ALIRAN DATA**

**➡️ Data Flow dari External Entity ke Sistem:**

```
Customer ──[Data Registrasi]──► Sistem
Customer ──[Data Login]───────► Sistem
Customer ──[Data Pemesanan]───► Sistem
Customer ──[Request Pembayaran]► Sistem

Admin ──[Data Login Admin]────► Sistem
Admin ──[Data Produk]─────────► Sistem
Admin ──[Data Konfirmasi Rental]► Sistem
Admin ──[Data Overtime]───────► Sistem
```

**⬅️ Data Flow dari Sistem ke External Entity:**

```
Sistem ──[Konfirmasi Registrasi]──► Customer
Sistem ──[Status Pemesanan]───────► Customer
Sistem ──[Invoice]────────────────► Customer
Sistem ──[Status Pembayaran]──────► Customer
Sistem ──[Laporan Rental]─────────► Customer
Sistem ──[Notifikasi]─────────────► Customer

Sistem ──[Notifikasi Pesanan Baru]► Admin
Sistem ──[Laporan Pembayaran]─────► Admin
Sistem ──[Data Rental]────────────► Admin
Sistem ──[Dashboard Analytics]────► Admin
```

#### 3️⃣ **INTERPRETASI LEVEL 0**

**📋 Apa yang Level 0 Katakan:**

- **Boundary Sistem:** Apa yang masuk dan keluar dari sistem
- **External Interactions:** Siapa saja yang berinteraksi dengan sistem
- **High-Level Functions:** Fungsi utama sistem secara garis besar
- **Data Exchange:** Jenis data yang dipertukarkan

**🎯 Cara Membaca:**

1. **Mulai dari External Entities** - Identifikasi siapa yang menggunakan sistem
2. **Trace Input Data** - Apa yang diberikan ke sistem
3. **Trace Output Data** - Apa yang diterima dari sistem
4. **Pahami Boundary** - Apa yang termasuk dalam sistem, apa yang tidak

### 📊 ALUR LEVEL 0

#### 🔄 **ALUR CUSTOMER/USER**

```
CUSTOMER JOURNEY:

1. REGISTRASI & LOGIN
   Customer ──[Data Registrasi]──► Sistem ──[Konfirmasi Registrasi]──► Customer
   Customer ──[Data Login]───────► Sistem

2. PEMESANAN
   Customer ──[Data Pemesanan]───► Sistem ──[Status Pemesanan]──────► Customer

3. PEMBAYARAN
   Customer ──[Request Pembayaran]► Sistem ──[Invoice]──────────────► Customer
                                          ──[Status Pembayaran]────► Customer

4. NOTIFIKASI & LAPORAN
   Sistem ──[Notifikasi]─────────► Customer
   Sistem ──[Laporan Rental]─────► Customer
```

#### 🔄 **ALUR ADMIN**

```
ADMIN JOURNEY:

1. LOGIN & AKSES
   Admin ──[Data Login Admin]────► Sistem

2. KELOLA PRODUK
   Admin ──[Data Produk]─────────► Sistem

3. KELOLA RENTAL
   Admin ──[Data Konfirmasi Rental]► Sistem ──[Data Rental]────────► Admin
   Admin ──[Data Overtime]───────► Sistem

4. MONITORING & LAPORAN
   Sistem ──[Notifikasi Pesanan Baru]► Admin
   Sistem ──[Laporan Pembayaran]─────► Admin
   Sistem ──[Dashboard Analytics]────► Admin
```

## 📊 DFD LEVEL 1 (PROSES UTAMA)

### 🔍 CARA MEMBACA LEVEL 1

#### 1️⃣ **IDENTIFIKASI KOMPONEN**

**🔵 Proses (6 Lingkaran):**

```
P1: Kelola Autentikasi    P2: Kelola Produk      P3: Kelola Pemesanan
P4: Kelola Pembayaran     P5: Kelola Notifikasi  P6: Generate Laporan
```

**📦 Data Store (6 Penyimpanan):**

```
D1: Data User        D2: Data Produk      D3: Data Rental
D4: Data Payment     D5: Data Notification D6: Data Session
```

**⬜ External Entities (Sama dengan Level 0):**

```
Customer/User        Admin
```

#### 2️⃣ **MEMBACA KONEKSI**

**🔗 External Entity → Process:**

```
Customer → P1 (Kelola Autentikasi)
Customer → P3 (Kelola Pemesanan)
Customer → P4 (Kelola Pembayaran)

Admin → P1 (Kelola Autentikasi)
Admin → P2 (Kelola Produk)
Admin → P3 (Kelola Pemesanan)
Admin → P4 (Kelola Pembayaran)
```

**🔗 Process → External Entity:**

```
P1 → Customer (Konfirmasi Login)
P1 → Admin (Konfirmasi Login Admin)
P2 → Customer (Info Produk)
P3 → Customer (Status Pemesanan)
P4 → Customer (Status Pembayaran, Invoice)
P5 → Customer (Notifikasi)
P5 → Admin (Notifikasi Pesanan Baru)
P6 → Customer (Laporan Rental)
P6 → Admin (Laporan Pembayaran, Dashboard Analytics)
```

**🔗 Process ↔ Data Store:**

```
P1 ↔ D1 (Data User), D6 (Data Session)
P2 ↔ D2 (Data Produk)
P3 ↔ D1, D2, D3 (Data Rental)
P4 ↔ D1, D3, D4 (Data Payment)
P5 ↔ D5 (Data Notification)
P6 ↔ D1, D2, D3, D4 (semua data untuk laporan)
```

**🔗 Process → Process:**

```
P3 → P5 (Data Pesanan Baru)
P4 → P5 (Info Pembayaran)
```

#### 3️⃣ **INTERPRETASI LEVEL 1**

**📋 Apa yang Level 1 Katakan:**

- **Functional Decomposition:** Sistem dipecah menjadi 6 fungsi utama
- **Data Management:** Bagaimana data disimpan dan diakses
- **Process Interaction:** Bagaimana proses saling berinteraksi
- **Detailed Data Flow:** Aliran data yang lebih spesifik

### 📊 ALUR LEVEL 1

#### 🔄 **ALUR PROSES 1: KELOLA AUTENTIKASI**

```
REGISTRASI:
Customer ──[Data Registrasi]──► P1 ──[Data User Baru]──► D1
                                P1 ──[Session Data]────► D6
                                P1 ──[Konfirmasi Registrasi]──► Customer

LOGIN:
Customer ──[Data Login]──► P1 ◄──[Info User]──── D1
                          P1 ──[Session Data]──► D6
                          P1 ──[Konfirmasi Login]──► Customer

ADMIN LOGIN:
Admin ──[Data Login Admin]──► P1 ◄──[Info User]──── D1
                              P1 ──[Session Data]──► D6
                              P1 ──[Konfirmasi Login Admin]──► Admin
```

#### 🔄 **ALUR PROSES 2: KELOLA PRODUK**

```
TAMBAH PRODUK:
Admin ──[Data Produk Baru]──► P2 ──[Data Produk]──► D2

UPDATE PRODUK:
Admin ──[Update Produk]──► P2 ◄──[Info Produk]──── D2
                          P2 ──[Data Produk Updated]──► D2

PUBLIKASI:
P2 ◄──[Info Produk]──── D2
P2 ──[Info Produk]──► Customer
```

#### 🔄 **ALUR PROSES 3: KELOLA PEMESANAN**

```
PEMESANAN BARU:
Customer ──[Data Pemesanan]──► P3 ◄──[Info User]──── D1
                               P3 ◄──[Info Produk]── D2
                               P3 ──[Data Rental]──► D3
                               P3 ──[Status Pemesanan]──► Customer
                               P3 ──[Data Pesanan Baru]──► P5

KONFIRMASI ADMIN:
Admin ──[Konfirmasi Rental]──► P3 ◄──[Info Rental]── D3
                               P3 ──[Data Rental Updated]──► D3
```

#### 🔄 **ALUR PROSES 4: KELOLA PEMBAYARAN**

```
REQUEST PEMBAYARAN:
Customer ──[Request Pembayaran]──► P4 ◄──[Info User]──── D1
                                   P4 ◄──[Info Rental]── D3
                                   P4 ──[Data Payment]──► D4
                                   P4 ──[Status Pembayaran]──► Customer
                                   P4 ──[Invoice]──► Customer
                                   P4 ──[Info Pembayaran]──► P5

OVERTIME:
Admin ──[Data Overtime]──► P4 ◄──[Info Rental]── D3
                          P4 ──[Data Payment Updated]──► D4
```

#### 🔄 **ALUR PROSES 5: KELOLA NOTIFIKASI**

```
NOTIFIKASI PESANAN:
P3 ──[Data Pesanan Baru]──► P5 ──[Data Notifikasi]──► D5
                            P5 ──[Notifikasi Pesanan Baru]──► Admin

NOTIFIKASI PEMBAYARAN:
P4 ──[Info Pembayaran]──► P5 ──[Data Notifikasi]──► D5
                          P5 ──[Notifikasi]──► Customer
```

#### 🔄 **ALUR PROSES 6: GENERATE LAPORAN**

```
LAPORAN CUSTOMER:
P6 ◄──[Info User]──── D1
P6 ◄──[Info Rental]── D3
P6 ──[Laporan Rental]──► Customer

LAPORAN ADMIN:
P6 ◄──[Info User]──── D1
P6 ◄──[Info Produk]── D2
P6 ◄──[Info Rental]── D3
P6 ◄──[Info Payment]── D4
P6 ──[Laporan Pembayaran]──► Admin
P6 ──[Dashboard Analytics]──► Admin
```

## 🔄 PERBANDINGAN LEVEL 0 vs LEVEL 1

### 📊 **PERBEDAAN UTAMA:**

| Aspek          | Level 0         | Level 1                  |
| -------------- | --------------- | ------------------------ |
| **Abstraksi**  | Sangat tinggi   | Menengah                 |
| **Detail**     | Minimal         | Sedang                   |
| **Proses**     | 1 sistem        | 6 proses                 |
| **Data Store** | Tidak ada       | 6 data store             |
| **Fokus**      | Boundary sistem | Functional decomposition |
| **Audience**   | Stakeholder     | Developer/Analyst        |

### 🎯 **KONSISTENSI (BALANCING):**

**✅ Input/Output harus sama:**

```
Level 0: Customer ──[Data Pemesanan]──► Sistem
Level 1: Customer ──[Data Pemesanan]──► P3 (Kelola Pemesanan)
Status: BALANCED ✅

Level 0: Sistem ──[Status Pemesanan]──► Customer
Level 1: P3 ──[Status Pemesanan]──► Customer
Status: BALANCED ✅
```

## 🎯 TIPS MEMBACA EFEKTIF

### ✅ **LANGKAH SISTEMATIS:**

1. **Level 0 Dulu:**

   - Pahami boundary sistem
   - Identifikasi external entities
   - Trace input/output utama

2. **Lanjut Level 1:**

   - Identifikasi 6 proses utama
   - Pahami peran setiap data store
   - Trace aliran data antar proses

3. **Validasi Balancing:**
   - Pastikan input/output Level 0 = Level 1
   - Cek konsistensi naming
   - Validasi kelengkapan data flow

### 🔍 **FOKUS PEMBACAAN:**

**Level 0 → Pertanyaan Kunci:**

- Siapa yang menggunakan sistem?
- Apa yang masuk ke sistem?
- Apa yang keluar dari sistem?
- Apa boundary sistem?

**Level 1 → Pertanyaan Kunci:**

- Apa saja fungsi utama sistem?
- Bagaimana data disimpan?
- Bagaimana proses berinteraksi?
- Apakah balancing dengan Level 0?

## 📊 DIAGRAM VISUAL

### 🎯 **LEVEL 0 - CONTEXT DIAGRAM**

```
                    SISTEM RENTAL GANSET - LEVEL 0
                           (Context Diagram)

┌─────────────┐                    ┌─────────────────────┐                    ┌─────────────┐
│  Customer   │◄──── Konfirmasi ────┤                     ├──── Data Login ────►│    Admin    │
│    /User    │      Registrasi     │   Sistem Rental     │     Admin          │             │
│             │                     │      Ganset         │                    │             │
│             ├──── Data ──────────►│                     │◄─── Data ──────────┤             │
│             │   Registrasi        │         (0)         │   Produk           │             │
│             │                     │                     │                    │             │
│             ├──── Data ──────────►│                     │◄─── Data ──────────┤             │
│             │    Login            │                     │  Konfirmasi        │             │
│             │                     │                     │   Rental           │             │
│             ├──── Data ──────────►│                     │                    │             │
│             │  Pemesanan          │                     │◄─── Data ──────────┤             │
│             │                     │                     │  Overtime          │             │
│             ├──── Request ───────►│                     │                    │             │
│             │  Pembayaran         │                     ├──── Notifikasi ────►│             │
│             │                     │                     │  Pesanan Baru      │             │
│             │◄──── Status ────────┤                     │                    │             │
│             │   Pemesanan         │                     ├──── Laporan ──────►│             │
│             │                     │                     │  Pembayaran        │             │
│             │◄──── Invoice ───────┤                     │                    │             │
│             │                     │                     ├──── Data ─────────►│             │
│             │◄──── Status ────────┤                     │   Rental           │             │
│             │  Pembayaran         │                     │                    │             │
│             │                     │                     ├──── Dashboard ────►│             │
│             │◄──── Laporan ──────┤                     │  Analytics         │             │
│             │   Rental            │                     │                    │             │
│             │                     │                     │                    │             │
│             │◄──── Notifikasi ───┤                     │                    │             │
└─────────────┘                    └─────────────────────┘                    └─────────────┘

EXTERNAL ENTITIES: 2        PROCESSES: 1        DATA STORES: 0
```

### 🎯 **LEVEL 1 - PROSES UTAMA**

```
                    SISTEM RENTAL GANSET - LEVEL 1
                         (Main Processes)

External Entities:        Processes:                    Data Stores:
┌─────────────┐          ┌─────────────┐              ┌─────────────────┐
│  Customer   │          │      1      │              │ D1 Data User    │
│    /User    │◄────────►│   Kelola    │◄────────────►│                 │
│             │          │ Autentikasi │              └─────────────────┘
└─────────────┘          └─────────────┘
                                │                      ┌─────────────────┐
                                │                      │ D2 Data Produk  │
┌─────────────┐          ┌─────────────┐              │                 │
│    Admin    │◄────────►│      2      │◄────────────►│                 │
│             │          │   Kelola    │              └─────────────────┘
│             │          │   Produk    │
│             │          └─────────────┘              ┌─────────────────┐
│             │                 │                     │ D3 Data Rental  │
│             │          ┌─────────────┐              │                 │
│             │◄────────►│      3      │◄────────────►│                 │
│             │          │   Kelola    │              └─────────────────┘
│             │          │ Pemesanan   │
│             │          └─────────────┘              ┌─────────────────┐
│             │                 │                     │ D4 Data Payment │
│             │          ┌─────────────┐              │                 │
│             │◄────────►│      4      │◄────────────►│                 │
│             │          │   Kelola    │              └─────────────────┘
│             │          │ Pembayaran  │
│             │          └─────────────┘              ┌─────────────────┐
│             │                 │                     │D5 Data          │
│             │          ┌─────────────┐              │  Notification   │
│             │◄────────►│      5      │◄────────────►│                 │
│             │          │   Kelola    │              └─────────────────┘
│             │          │ Notifikasi  │
│             │          └─────────────┘              ┌─────────────────┐
│             │                 │                     │ D6 Data Session │
│             │          ┌─────────────┐              │                 │
│             │◄────────►│      6      │◄────────────►│                 │
└─────────────┘          │  Generate   │              └─────────────────┘
                         │  Laporan    │
                         └─────────────┘

EXTERNAL ENTITIES: 2        PROCESSES: 6        DATA STORES: 6
```

## 🎯 LATIHAN PRAKTIS

### 📝 **LATIHAN 1: TRACE LEVEL 0**

**Soal:** Ikuti alur customer melakukan pemesanan di Level 0

**Jawaban:**

```
1. Customer ──[Data Pemesanan]──► Sistem Rental Ganset
2. Sistem Rental Ganset ──[Status Pemesanan]──► Customer
```

**Interpretasi:**

- Customer memberikan data pemesanan ke sistem
- Sistem memproses dan memberikan status kembali ke customer
- Level 0 tidak menunjukkan detail bagaimana pemrosesan dilakukan

### 📝 **LATIHAN 2: TRACE LEVEL 1**

**Soal:** Ikuti alur yang sama di Level 1 dengan detail proses

**Jawaban:**

```
1. Customer ──[Data Pemesanan]──► P3 (Kelola Pemesanan)
2. P3 ◄──[Info User]──── D1 (Data User)
3. P3 ◄──[Info Produk]── D2 (Data Produk)
4. P3 ──[Data Rental]──► D3 (Data Rental)
5. P3 ──[Status Pemesanan]──► Customer
6. P3 ──[Data Pesanan Baru]──► P5 (Kelola Notifikasi)
7. P5 ──[Notifikasi Pesanan Baru]──► Admin
```

**Interpretasi:**

- Level 1 menunjukkan detail: proses mana yang menangani, data apa yang dibutuhkan, di mana data disimpan
- Ada interaksi antar proses (P3 → P5)
- Admin mendapat notifikasi otomatis

### 📝 **LATIHAN 3: VALIDASI BALANCING**

**Soal:** Periksa apakah input/output Level 0 dan Level 1 seimbang untuk alur pembayaran

**Level 0:**

```
Customer ──[Request Pembayaran]──► Sistem ──[Invoice]──► Customer
                                         ──[Status Pembayaran]──► Customer
```

**Level 1:**

```
Customer ──[Request Pembayaran]──► P4 ──[Invoice]──► Customer
                                   P4 ──[Status Pembayaran]──► Customer
```

**Hasil:** ✅ BALANCED - Input dan output sama persis

## 🔍 CHECKLIST PEMAHAMAN

### ✅ **LEVEL 0 - CONTEXT DIAGRAM:**

- [ ] Dapat mengidentifikasi 2 external entities
- [ ] Dapat menjelaskan boundary sistem
- [ ] Dapat menyebutkan semua input dari customer
- [ ] Dapat menyebutkan semua input dari admin
- [ ] Dapat menyebutkan semua output ke customer
- [ ] Dapat menyebutkan semua output ke admin
- [ ] Memahami sistem sebagai "black box"

### ✅ **LEVEL 1 - PROSES UTAMA:**

- [ ] Dapat mengidentifikasi 6 proses utama
- [ ] Dapat mengidentifikasi 6 data store
- [ ] Dapat menjelaskan fungsi setiap proses
- [ ] Dapat menjelaskan isi setiap data store
- [ ] Dapat trace aliran data antar proses
- [ ] Dapat menjelaskan interaksi process-to-process
- [ ] Memahami functional decomposition

### ✅ **BALANCING LEVEL 0 & 1:**

- [ ] Dapat memvalidasi input/output consistency
- [ ] Dapat menjelaskan perbedaan level abstraksi
- [ ] Dapat mapping external entity interactions
- [ ] Memahami kapan menggunakan level mana

## 🎯 KESIMPULAN

### 📊 **RINGKASAN PEMBELAJARAN:**

**Level 0 (Context Diagram):**

- **Tujuan:** Memahami boundary dan interaksi eksternal sistem
- **Fokus:** Apa yang masuk dan keluar dari sistem
- **Audience:** Stakeholder, management, user
- **Abstraksi:** Sangat tinggi (sistem sebagai black box)

**Level 1 (Main Processes):**

- **Tujuan:** Memahami functional decomposition sistem
- **Fokus:** Bagaimana sistem bekerja secara internal
- **Audience:** Developer, analyst, designer
- **Abstraksi:** Menengah (detail proses dan data)

### 🎯 **MANFAAT MENGUASAI LEVEL 0 & 1:**

1. **📋 Requirement Analysis** - Memahami kebutuhan sistem dengan jelas
2. **🔧 System Design** - Panduan untuk merancang arsitektur sistem
3. **💬 Communication** - Bahasa yang sama antara stakeholder dan developer
4. **✅ Validation** - Memastikan sistem sesuai dengan kebutuhan bisnis
5. **📖 Documentation** - Dokumentasi yang mudah dipahami semua pihak

---

**🎓 Selamat! Anda sekarang dapat membaca DFD Level 0 dan Level 1 dengan percaya diri dan sistematis.**

**💡 Next Step:** Praktikkan dengan sistem lain untuk memperkuat pemahaman Anda!
