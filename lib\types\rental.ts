export interface Rental {
    id: string;
    userId: string;
    productId: string;
    status: string;
    startDate: Date | string;
    arrivalTime: Date | string;
    duration: number;
    address: string;
    totalPrice: number;
    overtimeHours?: number;
    overtimeFee?: number;
    notes?: string;
    comments?: string;
    operationalStart?: Date | string | null;
    operationalEnd?: Date | string | null;
    createdAt: Date;
    updatedAt: Date;
    product?: {
        id: string;
        name: string;
        capacity: number;
        description?: string;
    };
    user?: {
        id: string;
        name: string;
        email: string;
    };
}

export interface RentalResult {
    success: boolean;
    message: string;
    rental?: Rental;
}

export interface RentalResponse {
    success: boolean;
    message?: string;
    data?: {
        id: string;
        snapToken?: string;
        redirectTo?: string;
        // ... properti lainnya jika diperlukan
    };
}
