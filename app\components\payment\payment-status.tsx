import { Badge } from "@/app/components/ui/badge";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/app/components/ui/card";
import { formatCurrency } from "@/lib/utils/format";
import {
  Lu<PERSON>heck,
  Lu<PERSON>lock,
  LuInfo,
  LuCheckCheck,
} from "react-icons/lu";
import { InvoiceDownloadButton } from "./invoice-download-button";

interface PaymentStatusProps {
  rental: {
    id: string;
    status: string;
    amount: number;
    product: {
      name: string;
    };
    payment: {
      id: string;
      status: string;
      deposit: number;
      remaining: number;
      overtime?: number | null;
    };
  };
  showActions?: boolean;
}

// Fungsi untuk mendapatkan badge status
function getStatusBadge(status: string) {
  switch (status.toLowerCase()) {
    case "fully_paid":
      return {
        label: "Lunas",
        variant: "default" as const,
        icon: <LuCheck className="h-4 w-4 mr-1 text-green-300" />
      };
    case "deposit_paid":
      return {
        label: "Deposit Dibayar",
        variant: "success" as const,
        icon: <LuCheck className="h-4 w-4 mr-1 text-green-300" />
      };
    case "deposit_pending":
      return {
        label: "Menunggu Deposit",
        variant: "secondary" as const,
        icon: <LuClock className="h-4 w-4 mr-1 text-yellow-300" />
      };
    case "failed":
      return {
        label: "Gagal",
        variant: "destructive" as const,
        icon: <LuInfo className="h-4 w-4 mr-1" />
      };
    default:
      return {
        label: "Menunggu",
        variant: "outline" as const,
        icon: <LuClock className="h-4 w-4 mr-1" />
      };
  }
}

export function PaymentStatus({ rental, showActions = true }: PaymentStatusProps) {
  const { payment } = rental;
  const statusBadge = getStatusBadge(payment.status);
  const hasOvertime = payment.overtimeCost && payment.overtimeCost > 0;
  const totalWithOvertime = rental.amount + (payment.overtimeCost || 0);

  return (
    <Card className="overflow-hidden mb-6">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-violet-50 dark:from-blue-950/40 dark:to-violet-950/40 border-b border-gray-100 dark:border-gray-800">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl">
            Informasi Pembayaran
          </CardTitle>
          <Badge variant={statusBadge.variant} className="flex items-center">
            {statusBadge.icon}
            {statusBadge.label}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        <div>
          <h3 className="text-lg font-medium mb-3">{rental.product.name}</h3>
          <div className="flex justify-between text-sm mb-2">
            <span className="text-gray-600 dark:text-gray-400">Progress Pembayaran</span>
            <span className="font-medium text-gray-900 dark:text-white">
              {payment.status === "FULLY_PAID" ? "100%" : payment.status === "DEPOSIT_PAID" ? "50%" : "0%"}
            </span>
          </div>
          <div className="h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
            <div
              className={`h-full rounded-full ${payment.status === "FULLY_PAID" ? "bg-green-500" : "bg-blue-500"}`}
              style={{ width: payment.status === "FULLY_PAID" ? "100%" : payment.status === "DEPOSIT_PAID" ? "50%" : "0%" }}
            ></div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Deposit (50%)</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">{formatCurrency(payment.deposit)}</p>
            <div className="mt-2 flex items-center text-xs">
              {payment.status !== "DEPOSIT_PENDING" ? (
                <span className="text-green-600 dark:text-green-400 flex items-center">
                  <LuCheck className="mr-1 h-3 w-3" /> Dibayar
                </span>
              ) : (
                <span className="text-yellow-600 dark:text-yellow-400 flex items-center">
                  <LuClock className="mr-1 h-3 w-3" /> Menunggu
                </span>
              )}
            </div>
          </div>

          <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Pelunasan (50%)</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">{formatCurrency(payment.remaining)}</p>
            <div className="mt-2 flex items-center text-xs">
              {payment.status === "FULLY_PAID" ? (
                <span className="text-green-600 dark:text-green-400 flex items-center">
                  <LuCheck className="mr-1 h-3 w-3" /> Dibayar
                </span>
              ) : (
                <span className="text-yellow-600 dark:text-yellow-400 flex items-center">
                  <LuClock className="mr-1 h-3 w-3" /> {payment.status === "DEPOSIT_PENDING" ? "Setelah deposit" : "Menunggu dibayar"}
                </span>
              )}
            </div>
          </div>

          <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
              {hasOvertime ? "Biaya Overtime" : "Status Biaya Overtime"}
            </p>
            {hasOvertime ? (
              <p className="text-lg font-semibold text-red-600 dark:text-red-400">{formatCurrency(payment.overtimeCost || 0)}</p>
            ) : (
              <p className="text-lg font-semibold text-gray-900 dark:text-white">Rp 0</p>
            )}
            <div className="mt-2 flex items-center text-xs">
              {hasOvertime ? (
                <span className="text-red-600 dark:text-red-400 flex items-center">
                  <LuInfo className="mr-1 h-3 w-3" /> Termasuk di pelunasan
                </span>
              ) : (
                <span className="text-green-600 dark:text-green-400 flex items-center">
                  <LuCheck className="mr-1 h-3 w-3" /> Tidak ada overtime
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Total yang harus dibayar */}
        {payment.status !== "FULLY_PAID" && (
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <LuInfo className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <span className="font-medium text-blue-800 dark:text-blue-300">
                  {payment.status === "DEPOSIT_PENDING"
                    ? "Deposit yang Harus Dibayar:"
                    : "Sisa Pembayaran yang Harus Dilunasi:"}
                </span>
              </div>
              <span className="text-xl font-bold text-blue-800 dark:text-blue-300">
                {payment.status === "DEPOSIT_PENDING"
                  ? formatCurrency(payment.deposit)
                  : formatCurrency(payment.remaining + (payment.overtimeCost || 0))}
              </span>
            </div>
            {hasOvertime && payment.status === "DEPOSIT_PAID" && (
              <p className="mt-2 text-sm text-blue-700 dark:text-blue-400 pl-7">
                *Termasuk biaya overtime sebesar {formatCurrency(payment.overtimeCost || 0)}
              </p>
            )}
          </div>
        )}

        {payment.status === "FULLY_PAID" && (
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <LuCheckCheck className="h-5 w-5 text-green-600 dark:text-green-400" />
                <span className="font-medium text-green-800 dark:text-green-300">Total Dibayar:</span>
              </div>
              <span className="text-xl font-bold text-green-800 dark:text-green-300">
                {formatCurrency(totalWithOvertime)}
              </span>
            </div>
          </div>
        )}
      </CardContent>

      {showActions && (
        <CardFooter className="px-6 py-4 border-t border-gray-100 dark:border-gray-700 flex flex-wrap items-center justify-between gap-4">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            ID Pembayaran: {payment.id}
          </div>
          <div className="flex gap-3">
            {payment.status !== "DEPOSIT_PENDING" ? (
              <InvoiceDownloadButton
                invoiceId={rental.id}
                label="Download Invoice"
                variant="default"
              />
            ) : (
              <div className="text-sm text-gray-500 dark:text-gray-400 italic">
                Invoice tersedia setelah deposit dibayar
              </div>
            )}
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
