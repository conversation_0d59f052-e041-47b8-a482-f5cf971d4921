import { getSession } from "@/lib/auth/server";
import { redirect, notFound } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { formatCurrency } from "@/lib/utils/format";
import { OperationStatusBadge } from "@/app/components/operation/operation-status-badge";
import { BackButton } from "@/app/components/ui/back-button";
import { calculateOvertimeRate } from "@/lib/utils/calculate";
import { IoAlertCircleOutline, IoTimeOutline, IoCalendarOutline, IoCashOutline, IoStopwatchOutline, IoHourglassOutline, IoDocumentTextOutline, IoLocationOutline, IoAlarmOutline } from "react-icons/io5";
import { RefreshButton } from "@/app/components/dashboard/refresh-button";
import { getOperationById } from "@/lib/data/operation";
import { getRentalById } from "@/lib/data/rental";
import { CountdownTimer } from "@/app/components/operation/countdown-timer";
import { Rental } from "@/lib/types/rental";
import Image from "next/image";
import { formatDuration } from "@/lib/utils/duration";

// Definisi interface untuk operasi rental
interface Operation extends Rental {
  payment?: {
    amount: number;
    deposit: number;
    status: string;
    remaining: number;
    overtime: number | null;
    id: string;
  };
  product: {
    id: string;
    name: string;
    capacity: number;
    price: number;
    overtimeRate: number | null;
    imageUrl: string | null;
    image: string | null;
  };
  endDate?: Date | string;
}

// Tambahkan dynamic export untuk memastikan halaman selalu segar
export const dynamic = 'force-dynamic';

// Fungsi untuk memformat tanggal tanpa menampilkan jam:menit
function formatDateOnly(date: Date | string): string {
  const d = new Date(date);
  const options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  };
  return new Intl.DateTimeFormat('id-ID', options).format(d);
}

// Fungsi untuk format tanggal dengan waktu (untuk detail operasi)
function formatDateWithTime(date: Date | string): string {
  const d = new Date(date);
  const options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  };
  const dateStr = new Intl.DateTimeFormat('id-ID', options).format(d);

  // Format waktu tanpa kata pukul
  const hours = d.getHours().toString().padStart(2, '0');
  const minutes = d.getMinutes().toString().padStart(2, '0');

  return `${dateStr} ${hours}.${minutes}`;
}

interface Props {
  params: Promise<{ id: string }>;
}

export default async function UserOperationDetailPage({ params }: Props) {
  // Make sure to await params to properly handle dynamic route params
  const { id } = await params;

  const session = await getSession();
  if (!session?.user) {
    redirect('/login');
  }

  // Coba menggunakan fungsi operasi baru
  // Jika tidak berhasil, fallback ke getRentalById
  let rental;
  try {
    const operationData = await getOperationById(id);
    if (!operationData) {
      notFound();
    }

    // Pastikan pengguna yang login adalah pemilik rental
    if (operationData.userId !== session.user.id) {
      notFound();
    }

    rental = operationData;
  } catch (error) {
    console.error("Error fetching operation data:", error);

    // Fallback ke fungsi getRentalById
    rental = await getRentalById(id);
    if (!rental || rental.userId !== session.user.id) {
      notFound();
    }
  }

  const getOperationStatus = () => {
    if (!rental.operationalStart) return "pending";
    if (!rental.operationalEnd) return "running";
    return "completed";
  };

  // Calculate total amount
  const baseAmount = (rental as Operation).payment?.amount || (rental as Operation).totalPrice || 0;

  // Hitung tarif overtime berdasarkan kapasitas genset atau tarif khusus
  const overtimeRate = calculateOvertimeRate(
    (rental as Operation).product?.capacity,
    (rental as Operation).product?.overtimeRate
  );
  const overtimeHours = rental.overtimeHours || 0;
  const overtimeAmount = overtimeHours * overtimeRate;

  const totalAmount = baseAmount + overtimeAmount;

  // Status operasi
  const status = getOperationStatus();

  return (
    <div className="max-w-5xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <BackButton href="/user/operations" />
        <RefreshButton />
      </div>

      <Card className="border-0 shadow-lg rounded-xl overflow-hidden bg-white dark:bg-gray-900">
        <div className="h-2 bg-gradient-to-r from-violet-500 to-indigo-500 dark:from-violet-600 dark:to-indigo-600"></div>
        <CardHeader className="pb-2 pt-6">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
            <div className="flex items-center gap-2">
              <IoDocumentTextOutline className="h-6 w-6 text-violet-600 dark:text-violet-400" />
              <CardTitle className="text-xl text-white dark:text-white font-bold bg-gradient-to-r from-violet-600 to-indigo-600 dark:from-violet-400 dark:to-indigo-400 bg-clip-text text-transparent">Detail Operasi Rental</CardTitle>
            </div>
            <OperationStatusBadge status={status} />
          </div>
        </CardHeader>
        <CardContent className="px-6 pb-6">
          <div className="space-y-8">
            {/* Info Produk */}
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 border border-gray-100 dark:border-gray-700 shadow-sm flex flex-col sm:flex-row items-start gap-6">
              <div className="w-24 h-24 bg-white dark:bg-gray-700 rounded-xl flex items-center justify-center overflow-hidden shadow-sm border border-gray-100 dark:border-gray-600">
                {((rental as Operation).product?.imageUrl || (rental as Operation).product?.image) ? (
                  <Image
                    src={(rental as Operation).product?.imageUrl || (rental as Operation).product?.image || ''}
                    alt={(rental as Operation).product?.name || "Genset"}
                    className="w-full h-full object-cover"
                    width={96}
                    height={96}
                  />
                ) : (
                  <div className="text-gray-400 dark:text-gray-500 text-xs text-center">Tidak ada gambar</div>
                )}
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100">{(rental as Operation).product?.name || "Produk"}</h3>
                <div className="flex items-center gap-1 mt-1">
                  <span className="inline-block w-2 h-2 rounded-full bg-gray-400 dark:bg-gray-500"></span>
                  <p className="text-gray-600 dark:text-gray-400">{(rental as Operation).product?.capacity || 0} KVA</p>
                </div>
                <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-sm text-gray-500 dark:text-gray-400">Lihat detail untuk informasi lebih lanjut tentang genset ini.</p>
                </div>
              </div>
            </div>



            {/* Status Operasi */}
            <div className="p-5 rounded-xl border border-gray-100 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800/50 dark:to-gray-900/80 shadow-sm">
              <div className="flex items-start gap-4">
                <div className={`rounded-full p-3 ${status === 'pending' ? 'bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/40 dark:to-yellow-800/40' :
                  status === 'running' ? 'bg-gradient-to-br from-violet-50 to-violet-100 dark:from-violet-900/40 dark:to-violet-800/40' :
                    'bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/40 dark:to-indigo-800/40'
                  } shadow-inner`}>
                  <IoTimeOutline className={`h-6 w-6 ${status === 'pending' ? 'text-yellow-600 dark:text-yellow-400' :
                    status === 'running' ? 'text-violet-600 dark:text-violet-400' :
                      'text-indigo-600 dark:text-indigo-400'
                    }`} />
                </div>
                <div className="flex-1">
                  <p className={`font-semibold text-lg ${status === 'pending' ? 'text-yellow-800 dark:text-yellow-300' :
                    status === 'running' ? 'text-violet-800 dark:text-violet-300' :
                      'text-indigo-800 dark:text-indigo-300'
                    }`}>
                    {status === 'pending' ? 'Menunggu Operasi' :
                      status === 'running' ? 'Sedang Beroperasi' :
                        'Operasi Selesai'}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400 mt-1">
                    {!rental.operationalStart && "Menunggu tim teknisi memulai operasi"}
                    {rental.operationalStart && !rental.operationalEnd && "Operasi sedang berjalan"}
                    {rental.operationalEnd && "Operasi telah selesai"}
                  </p>
                  {status === "running" && (
                    <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-100 dark:border-yellow-800/30 rounded-lg">
                      <p className="text-sm text-yellow-800 dark:text-yellow-300">
                        <strong>Catatan:</strong> Jika operasi melebihi batas waktu sewa,
                        biaya overtime sebesar {formatCurrency(overtimeRate)}/jam
                        akan dikenakan.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Informasi Operasi */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Periode Sewa */}
              <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-violet-50 dark:bg-violet-900/30 rounded-full">
                    <IoCalendarOutline className="w-5 h-5 text-violet-500 dark:text-violet-400" />
                  </div>
                  <p className="font-medium text-gray-700 dark:text-gray-300">Periode Sewa</p>
                </div>
                <div className="space-y-2 ml-12">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-violet-400 dark:bg-violet-500 mr-2"></div>
                    <p className="text-gray-800 dark:text-gray-300">Mulai: <span className="font-medium">{formatDateOnly(rental.startDate)}</span></p>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-indigo-400 dark:bg-indigo-500 mr-2"></div>
                    <p className="text-gray-800 dark:text-gray-300">Selesai: <span className="font-medium">{(rental as Operation).endDate ? formatDateOnly((rental as Operation).endDate!) : "Belum ditentukan"}</span></p>
                  </div>
                </div>
              </div>



              {/* Tarif Overtime */}
              <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-orange-50 dark:bg-orange-900/30 rounded-full">
                    <IoAlarmOutline className="w-5 h-5 text-orange-500 dark:text-orange-400" />
                  </div>
                  <p className="font-medium text-gray-700 dark:text-gray-300">Tarif Overtime</p>
                </div>
                <p className="text-gray-800 dark:text-gray-300 ml-12 font-medium">
                  {formatCurrency(overtimeRate)}/jam
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 ml-12 mt-1">
                  Dikenakan jika penggunaan melebihi durasi sewa
                </p>
              </div>

              {/* Alamat Pengiriman */}
              {rental.address && (
                <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-2 bg-emerald-50 dark:bg-emerald-900/30 rounded-full">
                      <IoLocationOutline className="w-5 h-5 text-emerald-500 dark:text-emerald-400" />
                    </div>
                    <p className="font-medium text-gray-700 dark:text-gray-300">Alamat Pengiriman</p>
                  </div>
                  <p className="text-gray-800 dark:text-gray-300 ml-12 font-medium">
                    {rental.address}
                  </p>
                </div>
              )}

              {/* Waktu Kedatangan */}
              {rental.arrivalTime && (
                <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-2 bg-violet-50 dark:bg-violet-900/30 rounded-full">
                      <IoStopwatchOutline className="w-5 h-5 text-violet-500 dark:text-violet-400" />
                    </div>
                    <p className="font-medium text-gray-700 dark:text-gray-300">Waktu Kedatangan</p>
                  </div>
                  <p className="text-gray-800 dark:text-gray-300 ml-12 font-medium">
                    {typeof rental.arrivalTime === 'string' ? rental.arrivalTime : formatDateWithTime(rental.arrivalTime)}
                  </p>
                </div>
              )}

              {/* Biaya */}
              <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-amber-50 dark:bg-amber-900/30 rounded-full">
                    <IoCashOutline className="w-5 h-5 text-amber-500 dark:text-amber-400" />
                  </div>
                  <p className="font-medium text-gray-700 dark:text-gray-300">Biaya</p>
                </div>
                <p className="text-gray-800 dark:text-gray-300 ml-12 font-medium">{formatCurrency(baseAmount)}</p>
              </div>
              {/* Durasi */}
              <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-indigo-50 dark:bg-indigo-900/30 rounded-full">
                    <IoHourglassOutline className="w-5 h-5 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <p className="font-medium text-gray-700 dark:text-gray-300">Durasi</p>
                </div>
                <p className="text-gray-800 dark:text-gray-300 ml-12 font-medium">
                  {formatDuration(rental.duration)}
                </p>
              </div>
            </div>



            {/* Waktu Operasi */}
            <div className="space-y-5">
              {rental.operationalStart && (
                <div className="bg-gradient-to-r from-sky-50 to-sky-100 dark:from-sky-900/30 dark:to-sky-800/30 p-5 rounded-xl border border-sky-200 dark:border-sky-800/30 shadow-sm">
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-sm">
                      <IoTimeOutline className="w-5 h-5 text-sky-600 dark:text-sky-400" />
                    </div>
                    <div>
                      <p className="font-medium text-sky-800 dark:text-sky-300 mb-1">Waktu Mulai Operasi</p>
                      <p className="text-sky-700 dark:text-sky-400 text-lg font-semibold">{formatDateWithTime(rental.operationalStart)}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Timer hitung mundur jika operasi sedang berlangsung */}
              {status === "running" && rental.operationalStart && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-5 rounded-xl border border-blue-200 dark:border-blue-800/30 shadow-sm">
                  <div className="flex items-start gap-3 mb-3">
                    <div className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-sm">
                      <IoStopwatchOutline className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <p className="font-medium text-blue-800 dark:text-blue-300">Durasi Operasi</p>
                  </div>
                  {rental.id && (
                    <div className="mt-2">
                      <CountdownTimer
                        operationalStart={new Date(rental.operationalStart)}
                        duration={String(rental.duration || "8_HOURS")}
                        rentalId={rental.id}
                        showPauseButton={false}
                      />
                    </div>
                  )}
                </div>
              )}

              {rental.operationalEnd && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-5 rounded-xl border border-blue-200 dark:border-blue-800/30 shadow-sm">
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-sm">
                      <IoTimeOutline className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="font-medium text-blue-800 dark:text-blue-300 mb-1">Waktu Selesai Operasi</p>
                      <p className="text-blue-700 dark:text-blue-400 text-lg font-semibold">{formatDateWithTime(rental.operationalEnd)}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Overtime */}
              {overtimeHours > 0 && (
                <div className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/30 dark:to-amber-900/30 p-5 rounded-xl border border-orange-200 dark:border-orange-800/30 shadow-sm">
                  <div className="flex items-start gap-3 mb-4">
                    <div className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-sm">
                      <IoHourglassOutline className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                    </div>
                    <p className="font-semibold text-lg text-orange-800 dark:text-orange-300">Overtime: {overtimeHours} jam</p>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-orange-100 dark:border-orange-900/20">
                    <div className="flex justify-between text-sm mb-3">
                      <span className="text-orange-700 dark:text-orange-400">Tarif per jam</span>
                      <span className="font-medium text-orange-700 dark:text-orange-400">{formatCurrency(overtimeRate)}</span>
                    </div>
                    <div className="flex justify-between font-medium text-orange-800 dark:text-orange-300 pt-3 border-t border-orange-100 dark:border-orange-900/20">
                      <span>Total Biaya Overtime</span>
                      <span className="text-lg">{formatCurrency(overtimeAmount)}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Rincian Biaya */}
            <div className="bg-gradient-to-r from-gray-50 to-white dark:from-gray-800/50 dark:to-gray-900 p-5 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-sm">
                  <IoCashOutline className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </div>
                <h3 className="font-semibold text-lg text-gray-800 dark:text-gray-200">Rincian Biaya</h3>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 space-y-3">
                <div className="flex justify-between text-gray-700 dark:text-gray-300">
                  <span>Biaya Sewa Dasar</span>
                  <span className="font-medium">{formatCurrency(baseAmount)}</span>
                </div>

                {overtimeHours > 0 && (
                  <div className="flex justify-between text-orange-700 dark:text-orange-400">
                    <span>Biaya Overtime ({overtimeHours} jam)</span>
                    <span className="font-medium">{formatCurrency(overtimeAmount)}</span>
                  </div>
                )}

                <div className="flex justify-between font-semibold text-lg pt-3 border-t border-gray-100 dark:border-gray-700">
                  <span className="dark:text-gray-200">Total</span>
                  <span className="text-blue-700 dark:text-blue-400">{formatCurrency(totalAmount)}</span>
                </div>
              </div>
            </div>

            {/* Informasi refresh halaman */}
            <div className="p-4 bg-blue-50 dark:bg-blue-950/30 rounded-xl border border-blue-100 dark:border-blue-900/50 flex items-start gap-3 shadow-sm">
              <IoAlertCircleOutline className="h-6 w-6 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Status operasi diperbarui secara berkala oleh tim teknisi kami. Jika status operasi tidak sesuai, silahkan refresh halaman ini untuk melihat status terbaru.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 