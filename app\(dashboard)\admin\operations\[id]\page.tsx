import { prisma } from "@/lib/config/prisma";
import { redirect } from "next/navigation";
import { getSession } from "@/lib/auth/server";
import { formatCurrency, formatDateTime } from "@/lib/utils/format";
import { Rental } from "@/lib/types/rental";
import { calculateOvertimeRate } from "@/lib/utils/calculate";
import Link from "next/link";
import { OperationControl } from "@/app/components/operation/operation-control";
import { CountdownTimer } from "@/app/components/operation/countdown-timer";
import { Button } from "@/app/components/ui/button";
import { MoreVertical, ArrowLeft, FileText, User, Play, CheckSquare } from "lucide-react";

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { OperationStatusBadge } from "@/app/components/operation/operation-status-badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/app/components/ui/dropdown-menu";

// Pastikan halaman selalu diperbarui
export const dynamic = 'force-dynamic';

// Extended rental type with possible additional fields
type ExtendedRental = Rental & {
  notes?: string | null;
  endDate?: Date | string;
  amount?: number;
  payment?: {
    deposit: number;
    remaining: number;
    status: string;
    overtime?: number | null;
  } | null;
};

// Product type with overtime rate
interface ProductWithRate {
  capacity: number;
  overtimeRate?: number | null;
  name: string;
}

export default async function AdminOperationDetailPage({ params }: { params: Promise<{ id: string }> }) {
  // Await params sebelum mengakses propertinya
  const { id } = await params;

  const session = await getSession();
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil data lengkap rental dari database
  const rental = await prisma.rental.findUnique({
    where: {
      id: id
    },
    include: {
      product: true,
      user: {
        select: {
          name: true,
          email: true,
          phone: true
        }
      },
      payment: true
    }
  }) as ExtendedRental | null;

  // Check if rental exists before further processing
  if (!rental) {
    redirect("/admin/operations");
  }

  // Determine rental status
  let status: "pending" | "running" | "completed" | "cancelled" = "pending";
  if (rental.operationalStart && !rental.operationalEnd) {
    status = "running";
  } else if (rental.operationalEnd) {
    status = "completed";
  } else if (rental.status === "cancelled") {
    status = "cancelled";
  }

  // Calculate overtime rate and charge
  const overtimeRate = calculateOvertimeRate(
    (rental.product as unknown as ProductWithRate).capacity,
    (rental.product as unknown as ProductWithRate).overtimeRate || null
  );
  let overtimeHours = 0;

  if (rental.operationalEnd && rental.endDate) {
    // Calculate overtime hours if operation has ended
    const endTime = new Date(rental.operationalEnd as Date).getTime();
    const dueTime = new Date(rental.endDate as Date).getTime();
    overtimeHours = Math.max(0, Math.ceil((endTime - dueTime) / (1000 * 60 * 60)));

    // If overtime is already stored in database, use that value instead
    if (rental.overtimeHours !== null && rental.overtimeHours !== undefined) {
      overtimeHours = rental.overtimeHours;
    }
  }

  const overtimeAmount = overtimeHours * overtimeRate;
  const totalAmount = (rental.amount || 0) + overtimeAmount;

  // Cek status pembayaran
  const isDepositPaid = rental.payment?.status === "deposit_paid" ||
    rental.payment?.status === "fully_paid";

  // Hitung deposit dan sisa pembayaran
  const depositAmount = Math.floor((rental.amount || 0) * 0.5);
  const remainingAmount = Math.floor((rental.amount || 0) * 0.5) + overtimeAmount;

  // Perbaiki perbandingan status
  const isRunning = status === "running";
  const isCompleted = status === "completed";

  // Konversi rental ke format yang diterima oleh OperationControl
  const rentalForOperation = {
    id: rental.id,
    startDate: rental.startDate,
    endDate: rental.endDate,
    status: rental.status,
    amount: rental.amount || 0,
    duration: rental.duration || "", // Gunakan duration yang ada
    operationalStart: rental.operationalStart,
    operationalEnd: rental.operationalEnd,
    payment: rental.payment ? {
      deposit: rental.payment.deposit || 0,
      remaining: rental.payment.remaining || 0,
      overtime: rental.payment.overtime || null,
      status: rental.payment.status
    } : null
  };

  return (
    <div className="max-w-3xl mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <Link href="/admin/operations">
          <Button variant="outline" size="sm" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">
            <ArrowLeft className="h-4 w-4 mr-2 dark:text-gray-300" /> Kembali
          </Button>
        </Link>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">
              <MoreVertical className="h-4 w-4 dark:text-gray-300" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="dark:bg-gray-800 dark:border-gray-700">
            <DropdownMenuItem className="p-0 hover:bg-gray-100 dark:hover:bg-gray-700">
              <Link href={`/admin/rentals/${rental.id}`} className="flex items-center w-full px-2 py-1.5 text-gray-700 dark:text-gray-200">
                <FileText className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                <span>Lihat Detail Rental</span>
              </Link>
            </DropdownMenuItem>
            {rental.user && (
              <DropdownMenuItem className="p-0 hover:bg-gray-100 dark:hover:bg-gray-700">
                <Link href={`/admin/users/${rental.user.id}`} className="flex items-center w-full px-2 py-1.5 text-gray-700 dark:text-gray-200">
                  <User className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                  <span>Lihat Profil Pelanggan</span>
                </Link>
              </DropdownMenuItem>
            )}

            {!rental.operationalStart && (
              <>
                <DropdownMenuSeparator className="dark:border-gray-700" />
                <DropdownMenuItem className="p-0 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <Link href={`/admin/operations/${rental.id}/start`} className="flex items-center w-full px-2 py-1.5 text-gray-700 dark:text-gray-200">
                    <Play className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                    <span>Mulai Operasi</span>
                  </Link>
                </DropdownMenuItem>
              </>
            )}

            {rental.operationalStart && !rental.operationalEnd && (
              <>
                <DropdownMenuSeparator className="dark:border-gray-700" />
                <DropdownMenuItem className="p-0 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <Link href={`/admin/operations/${rental.id}/complete`} className="flex items-center w-full px-2 py-1.5 text-gray-700 dark:text-gray-200">
                    <CheckSquare className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                    <span>Selesaikan Operasi</span>
                  </Link>
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Status Operasi Rental</CardTitle>
          <OperationStatusBadge status={status} />
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <h3 className="font-medium">{(rental.product as unknown as ProductWithRate).name}</h3>

            {/* Tombol kontrol operasi hanya tampil jika belum selesai */}
            {!isCompleted && (
              <div className="mb-4">
                <OperationControl rental={rentalForOperation} isAdmin={true} />
              </div>
            )}

            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Periode Sewa</p>
                <p>{formatDateTime(rental.startDate as Date)} - {formatDateTime(rental.endDate as Date)}</p>
              </div>

              {rental.operationalStart && (
                <div>
                  <p className="text-sm text-gray-500">Waktu Mulai Operasi</p>
                  <p>{formatDateTime(rental.operationalStart as Date)}</p>
                </div>
              )}

              {/* Tampilkan timer hitung mundur jika operasi sedang berlangsung */}
              {status === "running" && rental?.operationalStart && (
                <div className="my-4">
                  <p className="text-sm text-gray-500 mb-2">Durasi Operasi</p>
                  <CountdownTimer
                    operationalStart={new Date(rental.operationalStart as Date)}
                    duration={typeof rental.duration === 'number'
                      ? rental.duration.toString()
                      : rental.duration as string}
                    rentalId={rental.id}
                    showPauseButton={false}
                  />
                </div>
              )}

              {rental.operationalEnd && (
                <div>
                  <p className="text-sm text-gray-500">Waktu Selesai Operasi</p>
                  <p>{formatDateTime(rental.operationalEnd as Date)}</p>
                </div>
              )}

              {overtimeHours > 0 && (
                <div className="p-3 bg-orange-50 border border-orange-200 rounded-md">
                  <p className="text-orange-700 font-medium">Overtime</p>
                  <div className="flex justify-between text-sm text-orange-600 mt-1">
                    <span>Durasi</span>
                    <span>{overtimeHours} jam</span>
                  </div>
                  <div className="flex justify-between text-sm text-orange-600">
                    <span>Tarif per jam</span>
                    <span>{formatCurrency(overtimeRate)}</span>
                  </div>
                  <div className="flex justify-between font-medium text-orange-700 mt-1 pt-1 border-t border-orange-200">
                    <span>Total Biaya Overtime</span>
                    <span>{formatCurrency(overtimeAmount)}</span>
                  </div>
                </div>
              )}
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">
                {!rental.operationalStart && "Menunggu tim teknisi memulai operasi"}
                {rental.operationalStart && !rental.operationalEnd && "Operasi sedang berjalan"}
                {rental.operationalEnd && "Operasi telah selesai"}
              </p>
              {isRunning && (
                <p className="text-sm text-gray-600 mt-2">
                  <strong>Catatan:</strong> Jika operasi melebihi batas waktu sewa,
                  biaya overtime sebesar {formatCurrency(overtimeRate)}/jam
                  akan dikenakan.
                </p>
              )}
            </div>

            {/* Rincian Biaya */}
            <div className="border-t pt-4">
              <h3 className="font-medium mb-3">Rincian Biaya</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Biaya Sewa Dasar</span>
                  <span>{formatCurrency(rental.amount || 0)}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span>Deposit (50%)</span>
                  <span className={isDepositPaid ? "text-green-600 font-medium" : ""}>
                    {formatCurrency(depositAmount)}
                    {isDepositPaid && " ✓"}
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span>Sisa Pembayaran (50%)</span>
                  <span>{formatCurrency(Math.floor((rental.amount || 0) * 0.5))}</span>
                </div>

                {overtimeHours > 0 && (
                  <div className="flex justify-between text-sm text-orange-700">
                    <span>Biaya Overtime ({overtimeHours} jam)</span>
                    <span>{formatCurrency(overtimeAmount)}</span>
                  </div>
                )}

                <div className="flex justify-between font-bold text-lg pt-2 border-t">
                  <span>Total Biaya Akhir</span>
                  <span>{formatCurrency(totalAmount)}</span>
                </div>

                {isDepositPaid && (
                  <div className="flex justify-between text-sm text-green-700 pt-2">
                    <span>Sudah Dibayar (Deposit)</span>
                    <span>{formatCurrency(depositAmount)}</span>
                  </div>
                )}

                {isDepositPaid && (
                  <div className="flex justify-between font-medium text-blue-700 pt-2">
                    <span>Sisa Tagihan</span>
                    <span>{formatCurrency(remainingAmount)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 